"use strict";exports.id=3582,exports.ids=[3582],exports.modules={3582:(e,t,r)=>{r.d(t,{AX:()=>$,COLLECTIONS:()=>i,FIELD_NAMES:()=>n,Gl:()=>V,I0:()=>g,II:()=>b,IK:()=>D,Pp:()=>c,Q6:()=>k,QD:()=>_,Ss:()=>S,YG:()=>h,_f:()=>x,addTransaction:()=>u,b6:()=>l,bA:()=>G,fP:()=>I,getUserData:()=>s,i8:()=>U,iA:()=>P,k6:()=>d,mm:()=>p,mv:()=>N,pl:()=>f,pu:()=>C,ul:()=>B,updateWalletBalance:()=>w,w1:()=>A,wT:()=>E,x4:()=>H,xj:()=>M,yx:()=>y,z8:()=>q,zb:()=>m});var a=r(75535),o=r(33784);let n={name:"name",email:"email",mobile:"mobile",referralCode:"referralCode",referredBy:"referredBy",referralBonusCredited:"referralBonusCredited",plan:"plan",planExpiry:"planExpiry",activeDays:"activeDays",joinedDate:"joinedDate",wallet:"wallet",bankAccountHolderName:"bankAccountHolderName",bankAccountNumber:"bankAccountNumber",bankIfscCode:"bankIfscCode",bankName:"bankName",bankDetailsUpdated:"bankDetailsUpdated",totalVideos:"totalVideos",todayVideos:"todayVideos",lastVideoDate:"lastVideoDate",videoDuration:"videoDuration",quickVideoAdvantage:"quickVideoAdvantage",quickVideoAdvantageExpiry:"quickVideoAdvantageExpiry",quickVideoAdvantageDays:"quickVideoAdvantageDays",quickVideoAdvantageSeconds:"quickVideoAdvantageSeconds",quickVideoAdvantageGrantedBy:"quickVideoAdvantageGrantedBy",quickVideoAdvantageGrantedAt:"quickVideoAdvantageGrantedAt",type:"type",amount:"amount",date:"date",status:"status",description:"description",userId:"userId"},i={users:"users",transactions:"transactions",withdrawals:"withdrawals",plans:"plans",settings:"settings",notifications:"notifications",adminLeaves:"adminLeaves",userLeaves:"userLeaves"};async function s(e){try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getUserData:",e),null;let t=await (0,a.x7)((0,a.H9)(o.db,i.users,e));if(t.exists()){let e=t.data(),r={name:String(e[n.name]||""),email:String(e[n.email]||""),mobile:String(e[n.mobile]||""),referralCode:String(e[n.referralCode]||""),referredBy:String(e[n.referredBy]||""),plan:String(e[n.plan]||"Trial"),planExpiry:e[n.planExpiry]?.toDate()||null,activeDays:Number(e[n.activeDays]||0),joinedDate:e[n.joinedDate]?.toDate()||new Date,videoDuration:Number(e[n.videoDuration]||("Trial"===e[n.plan]?30:300)),quickVideoAdvantage:!!e[n.quickVideoAdvantage],quickVideoAdvantageExpiry:e[n.quickVideoAdvantageExpiry]?.toDate()||null,quickVideoAdvantageDays:Number(e[n.quickVideoAdvantageDays]||0),quickVideoAdvantageSeconds:Number(e[n.quickVideoAdvantageSeconds]||30),quickVideoAdvantageGrantedBy:String(e[n.quickVideoAdvantageGrantedBy]||""),quickVideoAdvantageGrantedAt:e[n.quickVideoAdvantageGrantedAt]?.toDate()||null};return console.log("getUserData result:",r),r}return null}catch(e){return console.error("Error getting user data:",e),null}}async function d(e){try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getWalletData:",e),{wallet:0};let t=await (0,a.x7)((0,a.H9)(o.db,i.users,e));if(t.exists()){let e=t.data(),r={wallet:Number(e[n.wallet]||0)};return console.log("getWalletData result:",r),r}return{wallet:0}}catch(e){return console.error("Error getting wallet data:",e),{wallet:0}}}async function c(e){try{let t=await (0,a.x7)((0,a.H9)(o.db,i.users,e));if(t.exists()){let e=t.data(),r=e[n.totalVideos]||0,a=e[n.todayVideos]||0,o=e[n.lastVideoDate]?.toDate(),i=new Date,s=!o||o.toDateString()!==i.toDateString();return{totalVideos:r,todayVideos:s?0:a,remainingVideos:Math.max(0,50-(s?0:a))}}return{totalVideos:0,todayVideos:0,remainingVideos:50}}catch(e){throw console.error("Error getting video count data:",e),e}}async function l(e,t){try{await (0,a.mZ)((0,a.H9)(o.db,i.users,e),t)}catch(e){throw console.error("Error updating user data:",e),e}}async function u(e,t){try{let r={[n.userId]:e,[n.type]:t.type,[n.amount]:t.amount,[n.description]:t.description,[n.status]:t.status||"completed",[n.date]:a.Dc.now()};await (0,a.gS)((0,a.rJ)(o.db,i.transactions),r)}catch(e){throw console.error("Error adding transaction:",e),e}}async function g(e,t=10){try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getTransactions:",e),[];let r=(0,a.P)((0,a.rJ)(o.db,i.transactions),(0,a._M)(n.userId,"==",e),(0,a.AB)(t)),s=(await (0,a.GG)(r)).docs.map(e=>({id:e.id,...e.data(),date:e.data()[n.date]?.toDate()}));return s.sort((e,t)=>{let r=e.date||new Date(0);return(t.date||new Date(0)).getTime()-r.getTime()}),s}catch(e){return console.error("Error getting transactions:",e),[]}}async function f(e){try{let t=(0,a.P)((0,a.rJ)(o.db,i.users),(0,a._M)(n.referredBy,"==",e));return(await (0,a.GG)(t)).docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[n.joinedDate]?.toDate()}))}catch(e){throw console.error("Error getting referrals:",e),e}}async function y(e){try{let t=new Date,r=(0,a.H9)(o.db,i.users,e);await (0,a.mZ)(r,{[n.totalVideos]:(0,a.GV)(1),[n.todayVideos]:(0,a.GV)(1),[n.lastVideoDate]:a.Dc.fromDate(t)})}catch(e){throw console.error("Error updating video count:",e),e}}async function w(e,t){try{let r=(0,a.H9)(o.db,i.users,e);await (0,a.mZ)(r,{[n.wallet]:(0,a.GV)(t)})}catch(e){throw console.error("Error updating wallet balance:",e),e}}async function p(e,t){try{if(!e||"string"!=typeof e)throw Error("Invalid userId provided");var r=t;let{accountHolderName:s,accountNumber:d,ifscCode:c,bankName:l}=r;if(!s||s.trim().length<2)throw Error("Account holder name must be at least 2 characters long");if(!d||!/^\d{9,18}$/.test(d.trim()))throw Error("Account number must be 9-18 digits");if(!c||!/^[A-Z]{4}0[A-Z0-9]{6}$/.test(c.trim().toUpperCase()))throw Error("Invalid IFSC code format (e.g., SBIN0001234)");if(!l||l.trim().length<2)throw Error("Bank name must be at least 2 characters long");let u=(0,a.H9)(o.db,i.users,e);await (0,a.mZ)(u,{[n.bankAccountHolderName]:t.accountHolderName.trim(),[n.bankAccountNumber]:t.accountNumber.trim(),[n.bankIfscCode]:t.ifscCode.trim().toUpperCase(),[n.bankName]:t.bankName.trim(),[n.bankDetailsUpdated]:a.Dc.now()}),console.log("Bank details saved successfully for user:",e)}catch(e){throw console.error("Error saving bank details:",e),e}}async function m(e){try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getBankDetails:",e),null;let t=await (0,a.x7)((0,a.H9)(o.db,i.users,e));if(t.exists()){let e=t.data();if(e[n.bankAccountNumber]){let t={accountHolderName:String(e[n.bankAccountHolderName]||""),accountNumber:String(e[n.bankAccountNumber]||""),ifscCode:String(e[n.bankIfscCode]||""),bankName:String(e[n.bankName]||"")};return console.log("getBankDetails result found"),t}}return console.log("No bank details found for user"),null}catch(e){return console.error("Error getting bank details:",e),null}}function v(e){return({Trial:2,Starter:30,Basic:30,Premium:30,Gold:30,Platinum:30,Diamond:30,499:30,1499:30,2999:30,3999:30,5999:30,9999:30})[e]||2}async function h(e){try{let t=await s(e);if(!t)return{expired:!0,reason:"User data not found"};if("Trial"===t.plan){let e=t.joinedDate||new Date,r=new Date,a=Math.floor((r.getTime()-e.getTime())/864e5),o=Math.max(0,2-a);return{expired:o<=0,reason:o<=0?"Trial period expired":void 0,daysLeft:o,activeDays:a}}if(t.planExpiry){let e=new Date,r=e>t.planExpiry,a=r?0:Math.ceil((t.planExpiry.getTime()-e.getTime())/864e5);return{expired:r,reason:r?"Plan subscription expired":void 0,daysLeft:a,activeDays:t.activeDays||0}}let r=v(t.plan),a=t.activeDays||0,o=Math.max(0,r-a),n=o<=0;return{expired:n,reason:n?`Plan validity period (${r} days) exceeded based on active days`:void 0,daysLeft:o,activeDays:a}}catch(e){return console.error("Error checking plan expiry:",e),{expired:!0,reason:"Error checking plan status"}}}async function b(e,t,r){try{let s=(0,a.H9)(o.db,i.users,e);if("Trial"===t)await (0,a.mZ)(s,{[n.planExpiry]:null});else{let o;if(r)o=r;else{let e=v(t),r=new Date;o=new Date(r.getTime()+24*e*36e5)}await (0,a.mZ)(s,{[n.planExpiry]:a.Dc.fromDate(o)}),console.log(`Updated plan expiry for user ${e} to ${o.toDateString()}`)}}catch(e){throw console.error("Error updating plan expiry:",e),e}}async function D(e,t,r){try{if("Trial"!==t||"Trial"===r)return void console.log("Referral bonus only applies when upgrading from Trial to paid plan");console.log(`Processing referral bonus for user ${e} upgrading from ${t} to ${r}`);let s=await (0,a.x7)((0,a.H9)(o.db,i.users,e));if(!s.exists())return void console.log("User not found");let d=s.data(),c=d[n.referredBy],l=d[n.referralBonusCredited];if(!c)return void console.log("User was not referred by anyone, skipping bonus processing");if(l)return void console.log("Referral bonus already credited for this user, skipping");console.log("Finding referrer with code:",c);let g=(0,a.P)((0,a.rJ)(o.db,i.users),(0,a._M)(n.referralCode,"==",c),(0,a.AB)(1)),f=await (0,a.GG)(g);if(f.empty)return void console.log("Referral code not found:",c);let y=f.docs[0].id,p={Trial:0,499:50,1499:150,2999:300,3999:400,5999:700,9999:1200,Starter:50,Basic:150,Premium:300,Gold:400,Platinum:700,Diamond:1200}[r]||0;if(console.log(`Found referrer: ${y}, bonus amount: ₹${p}`),p>0){await w(y,p);let t=(0,a.H9)(o.db,i.users,y);await (0,a.mZ)(t,{[n.totalVideos]:(0,a.GV)(50)});let s=(0,a.H9)(o.db,i.users,e);await (0,a.mZ)(s,{[n.referralBonusCredited]:!0}),await u(y,{type:"referral_bonus",amount:p,description:`Referral bonus for ${r} plan upgrade + 50 bonus videos (User: ${d[n.name]})`}),console.log(`✅ Referral bonus processed: ₹${p} + 50 videos for referrer ${y}`)}else console.log("No bonus amount calculated, skipping")}catch(e){console.error("❌ Error processing referral bonus:",e)}}async function k(e){try{var t;let r=await s(e);if(!r)return{videoDuration:30,earningPerBatch:10,plan:"Trial",hasQuickAdvantage:!1};let a=!!(t=r).quickVideoAdvantage&&!!t.quickVideoAdvantageExpiry&&new Date<t.quickVideoAdvantageExpiry,o=r.videoDuration;return a?o=r.quickVideoAdvantageSeconds||30:o&&"Trial"!==r.plan||(o=({Trial:30,Starter:300,Basic:300,Premium:300,Gold:180,Platinum:120,Diamond:60})[r.plan]||30),{videoDuration:o,earningPerBatch:({Trial:10,Starter:25,Basic:75,Premium:150,Gold:200,Platinum:250,Diamond:400})[r.plan]||10,plan:r.plan,hasQuickAdvantage:a,quickAdvantageExpiry:r.quickVideoAdvantageExpiry}}catch(e){return console.error("Error getting user video settings:",e),{videoDuration:30,earningPerBatch:10,plan:"Trial",hasQuickAdvantage:!1}}}async function A(e,t,r,s=30){try{if(t<=0||t>365)throw Error("Days must be between 1 and 365");if(s<1||s>420)throw Error("Seconds must be between 1 and 420 (7 minutes)");let d=new Date,c=new Date(d.getTime()+24*t*36e5),l=(0,a.H9)(o.db,i.users,e);return await (0,a.mZ)(l,{[n.quickVideoAdvantage]:!0,[n.quickVideoAdvantageExpiry]:a.Dc.fromDate(c),[n.quickVideoAdvantageDays]:t,[n.quickVideoAdvantageSeconds]:s,[n.quickVideoAdvantageGrantedBy]:r,[n.quickVideoAdvantageGrantedAt]:a.Dc.fromDate(d)}),console.log(`Granted quick video advantage to user ${e} for ${t} days until ${c.toDateString()}`),await u(e,{type:"quick_advantage_granted",amount:0,description:`Quick video advantage granted for ${t} days by ${r}`}),{success:!0,expiry:c}}catch(e){throw console.error("Error granting quick video advantage:",e),e}}async function E(e,t){try{let r=(0,a.H9)(o.db,i.users,e);return await (0,a.mZ)(r,{[n.quickVideoAdvantage]:!1,[n.quickVideoAdvantageExpiry]:null,[n.quickVideoAdvantageDays]:0,[n.quickVideoAdvantageSeconds]:30,[n.quickVideoAdvantageGrantedBy]:"",[n.quickVideoAdvantageGrantedAt]:null}),console.log(`Removed quick video advantage from user ${e}`),await u(e,{type:"quick_advantage_removed",amount:0,description:`Quick video advantage removed by ${t}`}),{success:!0}}catch(e){throw console.error("Error removing quick video advantage:",e),e}}async function V(e,t){try{let r=[1,10,30].includes(t),s=t>=60&&t<=420;if(!r&&!s)throw Error("Video duration must be 1, 10, or 30 seconds for quick duration, or between 1-7 minutes (60-420 seconds) for standard duration");let d=(0,a.H9)(o.db,i.users,e);await (0,a.mZ)(d,{[n.videoDuration]:t}),console.log(`Updated video duration for user ${e} to ${t} seconds`)}catch(e){throw console.error("Error updating user video duration:",e),e}}async function q(e){try{let t={title:e.title,message:e.message,type:e.type,targetUsers:e.targetUsers,userIds:e.userIds||[],createdAt:a.Dc.now(),createdBy:e.createdBy};console.log("Adding notification to Firestore:",t);let r=await (0,a.gS)((0,a.rJ)(o.db,i.notifications),t);console.log("Notification added successfully with ID:",r.id);let n=await (0,a.x7)(r);return n.exists()?console.log("Notification verified in database:",n.data()):console.warn("Notification not found after adding"),r.id}catch(e){throw console.error("Error adding notification:",e),e}}async function S(e,t=20){try{let r,n;if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getUserNotifications:",e),[];console.log(`Loading notifications for user: ${e}`);try{let e=(0,a.P)((0,a.rJ)(o.db,i.notifications),(0,a._M)("targetUsers","==","all"),(0,a.My)("createdAt","desc"),(0,a.AB)(t));r=await (0,a.GG)(e),console.log(`Found ${r.docs.length} notifications for all users`)}catch(n){console.warn("Error querying all users notifications, trying without orderBy:",n);let e=(0,a.P)((0,a.rJ)(o.db,i.notifications),(0,a._M)("targetUsers","==","all"),(0,a.AB)(t));r=await (0,a.GG)(e)}try{let r=(0,a.P)((0,a.rJ)(o.db,i.notifications),(0,a._M)("targetUsers","==","specific"),(0,a._M)("userIds","array-contains",e),(0,a.My)("createdAt","desc"),(0,a.AB)(t));n=await (0,a.GG)(r),console.log(`Found ${n.docs.length} notifications for specific user`)}catch(s){console.warn("Error querying specific user notifications, trying without orderBy:",s);let r=(0,a.P)((0,a.rJ)(o.db,i.notifications),(0,a._M)("targetUsers","==","specific"),(0,a._M)("userIds","array-contains",e),(0,a.AB)(t));n=await (0,a.GG)(r)}let s=[];r.docs.forEach(e=>{s.push({id:e.id,...e.data(),createdAt:e.data().createdAt?.toDate()||new Date})}),n.docs.forEach(e=>{s.push({id:e.id,...e.data(),createdAt:e.data().createdAt?.toDate()||new Date})}),s.sort((e,t)=>t.createdAt.getTime()-e.createdAt.getTime());let d=s.slice(0,t);return console.log(`Returning ${d.length} total notifications for user`),d}catch(e){return console.error("Error getting user notifications:",e),[]}}async function x(e=50){try{let t=(0,a.P)((0,a.rJ)(o.db,i.notifications),(0,a.My)("createdAt","desc"),(0,a.AB)(e));return(await (0,a.GG)(t)).docs.map(e=>({id:e.id,...e.data(),createdAt:e.data().createdAt?.toDate()||new Date}))}catch(e){return console.error("Error getting all notifications:",e),[]}}async function I(e){try{if(!e||"string"!=typeof e)throw Error("Invalid notification ID provided");console.log("Deleting notification:",e),await (0,a.kd)((0,a.H9)(o.db,i.notifications,e)),console.log("Notification deleted successfully")}catch(e){throw console.error("Error deleting notification:",e),e}}async function G(e,t){try{let r=JSON.parse(localStorage.getItem(`read_notifications_${t}`)||"[]");r.includes(e)||(r.push(e),localStorage.setItem(`read_notifications_${t}`,JSON.stringify(r)))}catch(e){console.error("Error marking notification as read:",e)}}function N(e,t){try{return JSON.parse(localStorage.getItem(`read_notifications_${t}`)||"[]").includes(e)}catch(e){return console.error("Error checking notification read status:",e),!1}}function B(e,t){try{let r=JSON.parse(localStorage.getItem(`read_notifications_${t}`)||"[]");return e.filter(e=>!r.includes(e.id)).length}catch(e){return console.error("Error getting unread notification count:",e),0}}async function $(e){try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getUnreadNotifications:",e),[];console.log(`Loading unread notifications for user: ${e}`);let t=await S(e,50),r=JSON.parse(localStorage.getItem(`read_notifications_${e}`)||"[]"),a=t.filter(e=>e.id&&!r.includes(e.id));return console.log(`Found ${a.length} unread notifications`),a}catch(e){return console.error("Error getting unread notifications:",e),[]}}async function P(e){try{return(await $(e)).length>0}catch(e){return console.error("Error checking for unread notifications:",e),!1}}async function T(e){try{let t=(0,a.P)((0,a.rJ)(o.db,i.withdrawals),(0,a._M)("userId","==",e),(0,a._M)("status","==","pending"),(0,a.AB)(1));return!(await (0,a.GG)(t)).empty}catch(e){return console.error("Error checking pending withdrawals:",e),!1}}async function _(e){try{let t=await s(e);if(!t)return{allowed:!1,reason:"Unable to verify user information. Please try again."};if("Trial"===t.plan)return{allowed:!1,reason:"Trial plan users cannot make withdrawals. Please upgrade to a paid plan to enable withdrawals."};if(await T(e))return{allowed:!1,reason:"You have a pending withdrawal request. Please wait for it to be processed before submitting a new request."};let a=new Date,o=a.getHours();if(o<10||o>=18)return{allowed:!1,reason:"Withdrawals are only allowed between 10:00 AM to 6:00 PM"};let{isAdminLeaveDay:n}=await r.e(7087).then(r.bind(r,87087));if(await n(a))return{allowed:!1,reason:"Withdrawals are not allowed on admin leave/holiday days"};let{isUserOnLeave:i}=await r.e(7087).then(r.bind(r,87087));if(await i(e,a))return{allowed:!1,reason:"Withdrawals are not allowed on your leave days"};return{allowed:!0}}catch(e){return console.error("Error checking withdrawal allowed:",e),{allowed:!1,reason:"Unable to verify withdrawal eligibility. Please try again."}}}async function M(e,t,r){try{if(t<50)throw Error("Minimum withdrawal amount is ₹50");let n=await _(e);if(!n.allowed)throw Error(n.reason);if((await d(e)).wallet<t)throw Error("Insufficient wallet balance");await w(e,-t),await u(e,{type:"withdrawal_request",amount:-t,description:`Withdrawal request submitted - ₹${t} debited from wallet`});let s={userId:e,amount:t,bankDetails:r,status:"pending",date:a.Dc.now(),createdAt:a.Dc.now()};return(await (0,a.gS)((0,a.rJ)(o.db,i.withdrawals),s)).id}catch(e){throw console.error("Error creating withdrawal request:",e),e}}async function U(e,t=20){try{let r=(0,a.P)((0,a.rJ)(o.db,i.withdrawals),(0,a._M)("userId","==",e),(0,a.My)("date","desc"),(0,a.AB)(t));return(await (0,a.GG)(r)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date?.toDate()}))}catch(e){return console.error("Error getting user withdrawals:",e),[]}}async function H(){try{try{let e=(0,a.rJ)(o.db,i.users),t=((await (0,a.d_)(e)).data().count+1).toString().padStart(4,"0");return`MYN${t}`}catch(r){console.warn("Failed to get count from server, using fallback method:",r);let e=Date.now().toString().slice(-4),t=Math.random().toString(36).substring(2,4).toUpperCase();return`MYN${e}${t}`}}catch(t){console.error("Error generating unique referral code:",t);let e=Date.now().toString().slice(-4);return`MYN${e}`}}async function C(){return H()}}};