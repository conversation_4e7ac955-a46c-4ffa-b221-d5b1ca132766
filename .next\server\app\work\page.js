(()=>{var e={};e.id=4246,e.ids=[4246,7087],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},25122:(e,t,r)=>{Promise.resolve().then(r.bind(r,92920))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30766:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\work\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\work\\page.tsx","default")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},47061:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c});var s=r(65239),a=r(48088),o=r(88170),n=r.n(o),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let c={children:["",{children:["work",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,30766)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\work\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\work\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/work/page",pathname:"/work",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},55986:(e,t,r)=>{"use strict";r.d(t,{l:()=>o});var s=r(43210),a=r(87087);function o({userId:e,checkInterval:t=3e4,enabled:r=!0}){let[o,n]=(0,s.useState)({blocked:!1,lastChecked:new Date}),[i,l]=(0,s.useState)(!1);return{leaveStatus:o,isChecking:i,checkLeaveStatus:(0,s.useCallback)(async()=>{if(e&&r)try{l(!0);let t=await (0,a.q8)(e);return n({blocked:t.blocked,reason:t.reason,lastChecked:new Date}),t}catch(e){return console.error("Error checking leave status:",e),n(e=>({...e,lastChecked:new Date})),{blocked:!1}}finally{l(!1)}},[e,r]),isBlocked:o.blocked}}},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},59007:(e,t,r)=>{"use strict";r.d(t,{CA:()=>c,No:()=>d,ZB:()=>m,iD:()=>u,tx:()=>h});let s={CURRENT_BATCH:"mytube_current_batch",BATCH_PREFIX:"mytube_batch_",VIDEO_INDEX:"mytube_video_index",TOTAL_VIDEOS:"mytube_total_videos",LAST_PROCESSED:"mytube_last_processed"};function a(e){for(let t of[/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,/youtube\.com\/v\/([^&\n?#]+)/]){let r=e.match(t);if(r)return r[1]}return null}function o(e){let t=a(e);return t?`https://www.youtube.com/embed/${t}`:e}function n(e,t){return`Video ${t+1}`}function i(e){let t=function(e){try{let t=localStorage.getItem(`${s.BATCH_PREFIX}${e}`);if(!t)return null;let r=JSON.parse(t);if(Date.now()-r.lastUpdated>864e5)return localStorage.removeItem(`${s.BATCH_PREFIX}${e}`),null;return r}catch(t){return console.error(`Error loading batch ${e}:`,t),null}}(e);return t?t.videos:[]}function l(){return i(parseInt(localStorage.getItem(s.CURRENT_BATCH)||"0"))}function c(){let e=(parseInt(localStorage.getItem(s.CURRENT_BATCH)||"0")+1)%Math.ceil(parseInt(localStorage.getItem(s.TOTAL_VIDEOS)||"0")/100);return localStorage.setItem(s.CURRENT_BATCH,e.toString()),i(e)}function d(){let e=parseInt(localStorage.getItem(s.TOTAL_VIDEOS)||"0"),t=parseInt(localStorage.getItem(s.CURRENT_BATCH)||"0"),r=Math.ceil(e/100),a=i(t);return{totalVideos:e,currentBatch:t,totalBatches:r,videosInCurrentBatch:a.length}}function u(){Object.keys(localStorage).forEach(e=>{(e.startsWith(s.BATCH_PREFIX)||Object.values(s).includes(e))&&localStorage.removeItem(e)}),console.log("Cleared all video storage")}async function h(){try{let e=await fetch("/Mytube.json");if(!e.ok)throw Error(`Failed to load videos: ${e.statusText}`);let t=await e.json();console.log("Raw video data loaded:",Object.keys(t).length,"entries");let r=[];return Array.isArray(t)?t.forEach((e,t)=>{Object.entries(e).forEach(([e,t])=>{let s=a(t);s&&r.push({id:`video_${r.length}_${s}`,title:n(t,r.length),url:t,embedUrl:o(t),duration:300,category:"General",batchIndex:Math.floor(r.length/100)})})}):Object.entries(t).forEach(([e,t],s)=>{let i=a(t);i&&r.push({id:`video_${r.length}_${i}`,title:n(t,r.length),url:t,embedUrl:o(t),duration:300,category:"General",batchIndex:Math.floor(r.length/100)})}),r}catch(e){throw console.error("Error loading videos from file:",e),e}}async function m(){try{if(!function(){let e=localStorage.getItem(s.LAST_PROCESSED);return!e||Date.now()-parseInt(e)>864e5}())return console.log("Using cached video data..."),l();{console.log("Loading fresh video data...");let e=await h();return!function(e){let t=Math.ceil(e.length/100);for(let a=0;a<t;a++){let t=100*a,o=Math.min(t+100,e.length),n=e.slice(t,o);var r=a;try{let e={batchNumber:r,videos:n,totalVideos:n.length,lastUpdated:Date.now()};localStorage.setItem(`${s.BATCH_PREFIX}${r}`,JSON.stringify(e))}catch(e){console.error(`Error saving batch ${r}:`,e)}}localStorage.setItem(s.TOTAL_VIDEOS,e.length.toString()),localStorage.setItem(s.CURRENT_BATCH,"0"),localStorage.setItem(s.LAST_PROCESSED,Date.now().toString()),console.log(`Saved ${e.length} videos in ${t} batches`)}(e),l()}}catch(t){console.error("Error initializing video system:",t);let e=l();if(e.length>0)return console.log("Using cached videos as fallback"),e;throw t}}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},87087:(e,t,r)=>{"use strict";r.d(t,{applyUserLeave:()=>u,cancelUserLeave:()=>m,createAdminLeave:()=>n,debugAdminLeaveStatus:()=>l,deleteAdminLeave:()=>c,getAdminLeaves:()=>i,getUserLeaves:()=>h,getUserMonthlyLeaveCount:()=>x,isAdminLeaveDay:()=>d,isUserOnLeave:()=>g,q8:()=>p});var s=r(33784),a=r(75535);let o={adminLeaves:"adminLeaves",userLeaves:"userLeaves"};async function n(e){try{return(await (0,a.gS)((0,a.rJ)(s.db,o.adminLeaves),{...e,date:a.Dc.fromDate(e.date),createdAt:a.Dc.now()})).id}catch(e){throw console.error("Error creating admin leave:",e),e}}async function i(){try{let e=(0,a.P)((0,a.rJ)(s.db,o.adminLeaves),(0,a.My)("date","asc")),t=(await (0,a.GG)(e)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate(),createdAt:e.data().createdAt.toDate()}));return console.log("\uD83D\uDCC5 All admin leaves:",t),t}catch(e){throw console.error("Error getting admin leaves:",e),e}}async function l(){try{let e=new Date;console.log("\uD83D\uDD0D Debug: Checking admin leave status for today:",e.toDateString());let t=await d(e);console.log("\uD83D\uDCCA Debug: Admin leave result:",t);let r=await i();console.log("\uD83D\uDCC5 Debug: All admin leaves in database:",r);let s=r.filter(t=>t.date.toDateString()===e.toDateString());console.log("\uD83D\uDCC5 Debug: Today's admin leaves:",s)}catch(e){console.error("❌ Debug: Error checking admin leave status:",e)}}async function c(e){try{await (0,a.kd)((0,a.H9)(s.db,o.adminLeaves,e))}catch(e){throw console.error("Error deleting admin leave:",e),e}}async function d(e){try{let t=new Date(e);t.setHours(0,0,0,0);let r=new Date(e);r.setHours(23,59,59,999),console.log("\uD83D\uDD0D Checking admin leave for date range:",t.toISOString(),"to",r.toISOString());let n=(0,a.P)((0,a.rJ)(s.db,o.adminLeaves),(0,a._M)("date",">=",a.Dc.fromDate(t)),(0,a._M)("date","<=",a.Dc.fromDate(r))),i=await (0,a.GG)(n),l=!i.empty;return l?console.log("\uD83D\uDCC5 Found admin leave(s) for today:",i.docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate()}))):console.log("\uD83D\uDCC5 No admin leaves found for today"),l}catch(e){return console.error("❌ Error checking admin leave day:",e),!1}}async function u(e){try{let t,r,n,i=new Date,l=i.getFullYear(),c=i.getMonth()+1,d=await x(e.userId,l,c),u="pending";return d<4&&(u="approved",t="system",n=a.Dc.now(),r=`Auto-approved: ${d+1}/4 monthly leaves used`),{id:(await (0,a.gS)((0,a.rJ)(s.db,o.userLeaves),{...e,date:a.Dc.fromDate(e.date),status:u,appliedAt:a.Dc.now(),...t&&{reviewedBy:t},...n&&{reviewedAt:n},...r&&{reviewNotes:r}})).id,autoApproved:"approved"===u,usedLeaves:d+ +("approved"===u),maxLeaves:4}}catch(e){throw console.error("Error applying user leave:",e),e}}async function h(e){try{let t=(0,a.P)((0,a.rJ)(s.db,o.userLeaves),(0,a._M)("userId","==",e),(0,a.My)("date","desc"));return(await (0,a.GG)(t)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate(),appliedAt:e.data().appliedAt.toDate(),reviewedAt:e.data().reviewedAt?.toDate()}))}catch(e){throw console.error("Error getting user leaves:",e),e}}async function m(e){try{await (0,a.kd)((0,a.H9)(s.db,o.userLeaves,e))}catch(e){throw console.error("Error cancelling user leave:",e),e}}async function x(e,t,r){try{let n=new Date(t,r-1,1),i=new Date(t,r,0,23,59,59,999),l=(0,a.P)((0,a.rJ)(s.db,o.userLeaves),(0,a._M)("userId","==",e),(0,a._M)("status","==","approved"),(0,a._M)("date",">=",a.Dc.fromDate(n)),(0,a._M)("date","<=",a.Dc.fromDate(i)));return(await (0,a.GG)(l)).size}catch(e){return console.error("Error getting user monthly leave count:",e),0}}async function g(e,t){try{let r=new Date(t);r.setHours(0,0,0,0);let n=new Date(t);n.setHours(23,59,59,999),console.log("\uD83D\uDD0D Checking user leave for user:",e,"on date range:",r.toISOString(),"to",n.toISOString());let i=(0,a.P)((0,a.rJ)(s.db,o.userLeaves),(0,a._M)("userId","==",e),(0,a._M)("status","==","approved"),(0,a._M)("date",">=",a.Dc.fromDate(r)),(0,a._M)("date","<=",a.Dc.fromDate(n))),l=await (0,a.GG)(i),c=!l.empty;return c?console.log("\uD83D\uDC64 Found user leave(s) for today:",l.docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate()}))):console.log("\uD83D\uDC64 No user leaves found for today"),c}catch(e){return console.error("❌ Error checking user leave day:",e),!1}}async function p(e){try{let t=new Date;console.log("\uD83D\uDD0D Checking work block status for user:",e,"on date:",t.toDateString());try{let e=await d(t);if(console.log("\uD83D\uDCC5 Admin leave check result:",e),e)return console.log("\uD83D\uDEAB Work blocked due to admin leave"),{blocked:!0,reason:"System maintenance/holiday"}}catch(e){console.error("❌ Error checking admin leave (allowing work to continue):",e)}try{let r=await g(e,t);if(console.log("\uD83D\uDC64 User leave check result:",r),r)return console.log("\uD83D\uDEAB Work blocked due to user leave"),{blocked:!0,reason:"You are on approved leave today"}}catch(e){console.error("❌ Error checking user leave (allowing work to continue):",e)}return console.log("✅ Work is not blocked"),{blocked:!1}}catch(e){return console.error("❌ Error checking work block status (allowing work to continue):",e),{blocked:!1}}}},90842:(e,t,r)=>{Promise.resolve().then(r.bind(r,30766))},91645:e=>{"use strict";e.exports=require("net")},92920:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var s=r(60687),a=r(43210),o=r(85814),n=r.n(o),i=r(87979),l=r(744),c=r(55986),d=r(3582),u=r(59007);r(87087);var h=r(98873),m=r(77567);function x(){let{user:e,loading:t}=(0,i.Nu)(),{hasBlockingNotifications:r,isChecking:o,markAllAsRead:x}=(0,l.J)(e?.uid||null),{isBlocked:g,leaveStatus:p,checkLeaveStatus:v}=(0,c.l)({userId:e?.uid||null,checkInterval:3e4,enabled:!!e}),[f,b]=(0,a.useState)(null),[y,w]=(0,a.useState)(0),[S,j]=(0,a.useState)(0),[N,k]=(0,a.useState)(50),[D,_]=(0,a.useState)(!1),[A,C]=(0,a.useState)(0),[E,I]=(0,a.useState)(!1),[M,T]=(0,a.useState)(!1),[$,q]=(0,a.useState)(!1),[P,O]=(0,a.useState)(0),[L,R]=(0,a.useState)([]),[U,B]=(0,a.useState)([]),[V,J]=(0,a.useState)(!1),[G,F]=(0,a.useState)([]),[H,W]=(0,a.useState)(0),[Y,z]=(0,a.useState)(!0),[Q,X]=(0,a.useState)({totalVideos:0,currentBatch:0,totalBatches:0,videosInCurrentBatch:0}),[K,Z]=(0,a.useState)({videoDuration:300,earningPerBatch:10,plan:"Trial",hasQuickAdvantage:!1,quickAdvantageExpiry:null}),[ee,et]=(0,a.useState)(null),[er,es]=(0,a.useState)(0),[ea,eo]=(0,a.useState)(0),[en,ei]=(0,a.useState)(!0),[el,ec]=(0,a.useState)(!1),[ed,eu]=(0,a.useState)(0),eh=(0,a.useRef)(null),em=(0,a.useRef)(null),ex=()=>{_(!1),C(0),q(!1),ec(!1),eu(0),em.current&&f&&(em.current.src=f.embedUrl),eh.current&&(clearInterval(eh.current),eh.current=null)},eg=async()=>{if(E&&!M&&!(P<50)){if(g)return void m.A.fire({icon:"warning",title:"Submission Not Available",text:p.reason||"Video submission is not available due to leave.",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});try{T(!0);let t=K.earningPerBatch;for(let t=0;t<50;t++)await (0,d.yx)(e.uid);await (0,d.updateWalletBalance)(e.uid,t),await (0,d.addTransaction)(e.uid,{type:"video_earning",amount:t,description:"Batch completion reward - 50 videos watched"});let r=y+50;w(r),j(S+50),k(Math.max(0,50-r));let s=new Date().toDateString(),a=`video_session_${e.uid}_${s}`,o=`watch_times_${e.uid}_${s}`;localStorage.removeItem(a),localStorage.removeItem(o),O(0),R([]),I(!1),ex(),m.A.fire({icon:"success",title:"\uD83C\uDF89 Congratulations!",text:`You earned ₹${t} for completing a batch of 50 videos! Your wallet has been updated.`,timer:4e3,showConfirmButton:!1})}catch(e){console.error("Error submitting videos:",e),m.A.fire({icon:"error",title:"Submission Failed",text:"There was an error submitting your videos. Please try again."})}finally{T(!1)}}},ep=e=>{let t=Math.floor(e/60);return`${t}:${(e%60).toString().padStart(2,"0")}`},ev=e=>{let t=Math.floor((new Date().getTime()-e.getTime())/6e4);if(t<1)return"Just now";if(t<60)return`${t} min ago`;let r=Math.floor(t/60);if(r<24)return`${r}h ago`;let s=Math.floor(r/24);return`${s}d ago`};return t||Y||o?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner mb-4"}),(0,s.jsx)("p",{className:"text-white",children:t?"Loading...":o?"Checking notifications...":"Loading videos..."})]})}):r&&e?(0,s.jsx)(h.A,{userId:e.uid,onAllRead:x}):(0,s.jsxs)("div",{className:"min-h-screen p-4",children:[(0,s.jsxs)("header",{className:"glass-card p-4 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)(n(),{href:"/dashboard",className:"glass-button px-4 py-2 text-white",children:[(0,s.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,s.jsx)("h1",{className:"text-xl font-bold text-white",children:"Watch Videos & Earn"}),(0,s.jsxs)("div",{className:"text-white text-right",children:[(0,s.jsxs)("p",{className:"text-sm",children:["Plan: ",K.plan]}),(0,s.jsxs)("p",{className:"text-sm",children:["₹",K.earningPerBatch,"/batch (50 videos)"]})]})]}),(0,s.jsx)("div",{className:"bg-blue-500/20 border border-blue-400/30 rounded-lg p-3 mb-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-center text-center",children:[(0,s.jsx)("i",{className:"fas fa-sync-alt text-blue-400 mr-2"}),(0,s.jsx)("span",{className:"text-white/90 text-sm",children:'Refresh page or click "Change Video" for different content'})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-5 gap-2 text-center",children:[(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,s.jsx)("p",{className:"text-lg font-bold text-yellow-400",children:er}),(0,s.jsx)("p",{className:"text-white/80 text-xs",children:"days left"})]}),(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,s.jsx)("p",{className:"text-lg font-bold text-blue-400",children:y}),(0,s.jsx)("p",{className:"text-white/80 text-xs",children:"Today's Videos"})]}),(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,s.jsx)("p",{className:"text-lg font-bold text-green-400",children:S}),(0,s.jsx)("p",{className:"text-white/80 text-xs",children:"Total Videos"})]}),(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,s.jsx)("p",{className:"text-lg font-bold text-purple-400",children:Math.max(0,50-y)}),(0,s.jsx)("p",{className:"text-white/80 text-xs",children:"Videos Left"})]}),(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,s.jsxs)("p",{className:"text-lg font-bold text-orange-400",children:[ea,"/","Trial"===K.plan?"2":"30"]}),(0,s.jsx)("p",{className:"text-white/80 text-xs",children:"Active Days"})]})]})]}),(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("h2",{className:"text-xl font-bold text-white",children:[(0,s.jsx)("i",{className:"fas fa-play-circle mr-2"}),"Watch Video & Earn"]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[K.hasQuickAdvantage&&(0,s.jsxs)("div",{className:"bg-green-500/20 border border-green-400/30 rounded-lg px-3 py-1",children:[(0,s.jsxs)("div",{className:"flex items-center text-green-300 text-sm",children:[(0,s.jsx)("i",{className:"fas fa-bolt mr-1"}),(0,s.jsx)("span",{className:"font-medium",children:"Quick Advantage Active"})]}),K.quickAdvantageExpiry&&(0,s.jsxs)("div",{className:"text-xs text-green-400 mt-1",children:["Until: ",new Date(K.quickAdvantageExpiry).toLocaleDateString()]})]}),(0,s.jsxs)("button",{onClick:()=>window.location.reload(),className:"glass-button px-3 py-1 text-white text-sm",title:"Refresh to change video",children:[(0,s.jsx)("i",{className:"fas fa-sync-alt mr-1"}),"Change Video"]})]})]}),f&&(0,s.jsxs)("div",{className:`aspect-video mb-4 video-container ${D?"watching":""}`,children:[(0,s.jsx)("iframe",{ref:em,src:D?`${f.embedUrl}?autoplay=1&mute=0&controls=1&rel=0&modestbranding=1&disablekb=1`:f.embedUrl,title:f.title,className:"w-full h-full rounded-lg border-0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0}),D&&(0,s.jsx)("div",{className:"video-protection-overlay rounded-lg"}),!D&&(0,s.jsx)("div",{className:"absolute inset-0 bg-black/30 backdrop-blur-sm rounded-lg flex items-center justify-center",children:(0,s.jsx)("div",{className:"text-center text-white",children:(0,s.jsx)("i",{className:"fas fa-play-circle text-6xl opacity-60 text-youtube-red"})})})]}),(0,s.jsx)("div",{className:"text-center",children:D?(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-white",children:ep(A)}),el&&(0,s.jsx)("div",{className:"bg-red-500/20 border border-red-400/30 rounded-lg p-3 mb-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-center text-center",children:[(0,s.jsx)("i",{className:"fas fa-pause text-red-400 mr-2"}),(0,s.jsx)("span",{className:"text-red-300 text-sm font-medium",children:"Timer Paused - Please stay on this page to continue watching"})]})}),(0,s.jsx)("div",{className:"bg-white/20 rounded-full h-3 max-w-md mx-auto",children:(0,s.jsx)("div",{className:`h-3 rounded-full transition-all duration-1000 ${el?"bg-red-500":"bg-youtube-red"}`,style:{width:`${(K.videoDuration-A)/K.videoDuration*100}%`}})}),(0,s.jsxs)("div",{className:"space-x-4",children:[(0,s.jsxs)("button",{onClick:ex,className:"btn-secondary",children:[(0,s.jsx)("i",{className:"fas fa-stop mr-2"}),"Stop Watching"]}),$&&(0,s.jsx)("button",{onClick:()=>{if(!$||M)return;if(g){ex(),m.A.fire({icon:"warning",title:"Work Suspended",text:p.reason||"Work has been suspended due to leave.",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});return}let t=P+1,r=new Date,s=[...L,r],a=[...U,r];O(t),R(s),B(a);let o=new Date().toDateString(),n=`video_session_${e.uid}_${o}`,i=`watch_times_${e.uid}_${o}`,l=`daily_watch_times_${e.uid}_${o}`;localStorage.setItem(n,t.toString()),localStorage.setItem(i,JSON.stringify(s.map(e=>e.toISOString()))),localStorage.setItem(l,JSON.stringify(a.map(e=>e.toISOString())));let c=H+1;if(c>=G.length)try{let e=(0,u.CA)();F(e);let r=Math.floor(Math.random()*e.length);W(r),b(e[r]);let s=(0,u.No)();X(s),m.A.fire({icon:"info",title:"New Video Batch Loaded",text:`Video ${t}/50 completed. Batch ${s.currentBatch+1}/${s.totalBatches} loaded.`,timer:2e3,showConfirmButton:!1})}catch(t){console.error("Error loading next batch:",t);let e=Math.floor(Math.random()*G.length);W(e),b(G[e])}else{if(.3>Math.random()&&G.length>3){let e=G.map((e,t)=>t).filter(e=>e!==H);c=e[Math.floor(Math.random()*e.length)],console.log(`Randomized next video: ${c} (was going to ${H+1})`)}W(c),b(G[c])}ex(),t<50?m.A.fire({icon:"success",title:"Video Completed!",text:`Progress: ${t}/50 videos watched. ${50-t} more to go!`,timer:2e3,showConfirmButton:!1}):m.A.fire({icon:"success",title:"\uD83C\uDF89 All Videos Completed!",text:'You have watched all 50 videos! Click "Submit & Earn" to get your rewards.',timer:3e3,showConfirmButton:!1})},disabled:M||!$,className:`${M?"btn-disabled cursor-not-allowed opacity-50":"btn-primary"}`,children:M?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Processing..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-arrow-right mr-2"}),"Next Video"]})})]})]}):(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("button",{onClick:()=>{if(!D){if(g)return void m.A.fire({icon:"warning",title:"Work Not Available",text:p.reason||"Work is currently blocked due to leave.",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});if(N<=0)return void m.A.fire({icon:"warning",title:"Daily Limit Reached",text:"You have reached your daily video limit. Come back tomorrow!"});if(_(!0),C(K.videoDuration),q(!1),ec(!1),eu(0),em.current&&f){let e=`${f.embedUrl}?autoplay=1&mute=0&controls=1&rel=0&modestbranding=1`;em.current.src=e}en?eh.current=setInterval(()=>{C(e=>e<=1?(q(!0),eh.current&&clearInterval(eh.current),0):e-1)},1e3):(ec(!0),eu(K.videoDuration))}},disabled:P>=50||D||M,className:`text-lg px-8 py-4 ${P>=50||D||M?"btn-disabled cursor-not-allowed opacity-50":K.hasQuickAdvantage?"btn-success bg-green-500 hover:bg-green-600":"btn-primary"}`,children:D?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Starting Video..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:`mr-2 ${K.hasQuickAdvantage?"fas fa-bolt":"fas fa-play"}`}),K.hasQuickAdvantage?"Quick Watch":"Start Watching"," (",ep(K.videoDuration),")"]})}),E&&P>=50&&(0,s.jsx)("button",{onClick:eg,disabled:M,className:"btn-success text-lg px-8 py-4 bg-green-500 hover:bg-green-600",children:M?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Submitting All Videos..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-trophy mr-2"}),"Submit & Earn ₹",K.earningPerBatch]})})]})})]}),U.length>0&&(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("h2",{className:"text-xl font-bold text-white",children:[(0,s.jsx)("i",{className:"fas fa-clock mr-2"}),"Today's Watch History"]}),(0,s.jsxs)("div",{className:"text-white/70 text-sm",children:["Total: ",U.length," videos watched"]})]}),(0,s.jsx)("div",{className:"max-h-64 overflow-y-auto",children:(0,s.jsx)("div",{className:"grid gap-2",children:U.map((e,t)=>(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-3 flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"bg-youtube-red/20 rounded-full p-2 mr-3",children:(0,s.jsx)("i",{className:"fas fa-play text-youtube-red text-sm"})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"text-white font-medium",children:["Video #",t+1]}),(0,s.jsx)("p",{className:"text-white/70 text-sm",children:e.toLocaleDateString("en-IN",{weekday:"short",year:"numeric",month:"short",day:"numeric"})})]})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsx)("p",{className:"text-white font-medium",children:e.toLocaleTimeString("en-IN",{hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!0})}),(0,s.jsx)("p",{className:"text-white/70 text-xs",children:ev(e)})]})]},t))})}),U.length>=50&&(0,s.jsx)("div",{className:"mt-4 bg-green-500/20 border border-green-400/30 rounded-lg p-3",children:(0,s.jsxs)("div",{className:"flex items-center justify-center text-center",children:[(0,s.jsx)("i",{className:"fas fa-trophy text-green-400 mr-2"}),(0,s.jsx)("span",{className:"text-green-300 text-sm font-medium",children:"Daily target completed! Great job! \uD83C\uDF89"})]})})]})]})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[6204,2756,7567,8441,3582,8126],()=>r(47061));module.exports=s})();