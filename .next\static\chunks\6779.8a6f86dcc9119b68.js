"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6779],{6779:(t,a,e)=>{e.d(a,{CF:()=>c,Pn:()=>s,TK:()=>w,getWithdrawals:()=>u,hG:()=>h,lo:()=>i,nQ:()=>E,updateWithdrawalStatus:()=>L,x5:()=>l});var r=e(5317),o=e(6104),d=e(3592);let n=new Map;async function s(){let t="dashboard-stats",a=function(t){let a=n.get(t);return a&&Date.now()-a.timestamp<3e5?a.data:null}(t);if(a)return a;try{let a=new Date;a.setHours(0,0,0,0);let e=r.Dc.fromDate(a),s=await (0,r.GG)((0,r.rJ)(o.db,d.COLLECTIONS.users)),i=s.size,l=(0,r.P)((0,r.rJ)(o.db,d.COLLECTIONS.users),(0,r._M)(d.FIELD_NAMES.joinedDate,">=",e)),c=(await (0,r.GG)(l)).size,E=0,u=0,w=0,h=0;s.forEach(t=>{var e;let r=t.data();E+=r[d.FIELD_NAMES.totalVideos]||0,u+=r[d.FIELD_NAMES.wallet]||0;let o=null==(e=r[d.FIELD_NAMES.lastVideoDate])?void 0:e.toDate();o&&o.toDateString()===a.toDateString()&&(w+=r[d.FIELD_NAMES.todayVideos]||0)});try{let t=(0,r.P)((0,r.rJ)(o.db,d.COLLECTIONS.transactions),(0,r._M)(d.FIELD_NAMES.type,"==","video_earning"),(0,r.AB)(1e3));(await (0,r.GG)(t)).forEach(t=>{var e;let r=t.data(),o=null==(e=r[d.FIELD_NAMES.date])?void 0:e.toDate();o&&o>=a&&(h+=r[d.FIELD_NAMES.amount]||0)})}catch(t){console.warn("Could not fetch today's transactions:",t)}let L=(0,r.P)((0,r.rJ)(o.db,d.COLLECTIONS.withdrawals),(0,r._M)("status","==","pending")),D=(await (0,r.GG)(L)).size,C=(0,r.P)((0,r.rJ)(o.db,d.COLLECTIONS.withdrawals),(0,r._M)("date",">=",e)),p=(await (0,r.GG)(C)).size,N={totalUsers:i,totalVideos:E,totalEarnings:u,pendingWithdrawals:D,todayUsers:c,todayVideos:w,todayEarnings:h,todayWithdrawals:p};return n.set(t,{data:N,timestamp:Date.now()}),N}catch(t){throw console.error("Error getting admin dashboard stats:",t),t}}async function i(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let e=(0,r.P)((0,r.rJ)(o.db,d.COLLECTIONS.users),(0,r.My)(d.FIELD_NAMES.joinedDate,"desc"),(0,r.AB)(t));a&&(e=(0,r.P)((0,r.rJ)(o.db,d.COLLECTIONS.users),(0,r.My)(d.FIELD_NAMES.joinedDate,"desc"),(0,r.HM)(a),(0,r.AB)(t)));let n=await (0,r.GG)(e);return{users:n.docs.map(t=>{var a,e;return{id:t.id,...t.data(),joinedDate:null==(a=t.data()[d.FIELD_NAMES.joinedDate])?void 0:a.toDate(),planExpiry:null==(e=t.data()[d.FIELD_NAMES.planExpiry])?void 0:e.toDate()}}),lastDoc:n.docs[n.docs.length-1]||null,hasMore:n.docs.length===t}}catch(t){throw console.error("Error getting users:",t),t}}async function l(t){try{if(!t||0===t.trim().length)return[];let a=t.toLowerCase().trim(),e=(0,r.P)((0,r.rJ)(o.db,d.COLLECTIONS.users),(0,r.My)(d.FIELD_NAMES.joinedDate,"desc"));return(await (0,r.GG)(e)).docs.map(t=>{var a,e;return{id:t.id,...t.data(),joinedDate:null==(a=t.data()[d.FIELD_NAMES.joinedDate])?void 0:a.toDate(),planExpiry:null==(e=t.data()[d.FIELD_NAMES.planExpiry])?void 0:e.toDate()}}).filter(t=>{let e=(t[d.FIELD_NAMES.name]||"").toLowerCase(),r=(t[d.FIELD_NAMES.email]||"").toLowerCase(),o=(t[d.FIELD_NAMES.mobile]||"").toLowerCase(),n=(t[d.FIELD_NAMES.referralCode]||"").toLowerCase();return e.includes(a)||r.includes(a)||o.includes(a)||n.includes(a)})}catch(t){throw console.error("Error searching users:",t),t}}async function c(){try{let t=(0,r.P)((0,r.rJ)(o.db,d.COLLECTIONS.users),(0,r.My)(d.FIELD_NAMES.joinedDate,"desc"));return(await (0,r.GG)(t)).docs.map(t=>{var a,e;return{id:t.id,...t.data(),joinedDate:null==(a=t.data()[d.FIELD_NAMES.joinedDate])?void 0:a.toDate(),planExpiry:null==(e=t.data()[d.FIELD_NAMES.planExpiry])?void 0:e.toDate()}})}catch(t){throw console.error("Error getting all users:",t),t}}async function E(){try{let t=(0,r.P)((0,r.rJ)(o.db,d.COLLECTIONS.users));return(await (0,r.GG)(t)).size}catch(t){throw console.error("Error getting total user count:",t),t}}async function u(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let e=(0,r.P)((0,r.rJ)(o.db,d.COLLECTIONS.withdrawals),(0,r.My)("date","desc"),(0,r.AB)(t));a&&(e=(0,r.P)((0,r.rJ)(o.db,d.COLLECTIONS.withdrawals),(0,r.My)("date","desc"),(0,r.HM)(a),(0,r.AB)(t)));let n=await (0,r.GG)(e);return{withdrawals:n.docs.map(t=>{var a;return{id:t.id,...t.data(),date:null==(a=t.data().date)?void 0:a.toDate()}}),lastDoc:n.docs[n.docs.length-1]||null,hasMore:n.docs.length===t}}catch(t){throw console.error("Error getting withdrawals:",t),t}}async function w(t,a){try{await (0,r.mZ)((0,r.H9)(o.db,d.COLLECTIONS.users,t),a),n.delete("dashboard-stats")}catch(t){throw console.error("Error updating user:",t),t}}async function h(t){try{await (0,r.kd)((0,r.H9)(o.db,d.COLLECTIONS.users,t)),n.delete("dashboard-stats")}catch(t){throw console.error("Error deleting user:",t),t}}async function L(t,a,s){try{let i=await (0,r.x7)((0,r.H9)(o.db,d.COLLECTIONS.withdrawals,t));if(!i.exists())throw Error("Withdrawal not found");let{userId:l,amount:c,status:E}=i.data(),u={status:a,updatedAt:r.Dc.now()};if(s&&(u.adminNotes=s),await (0,r.mZ)((0,r.H9)(o.db,d.COLLECTIONS.withdrawals,t),u),"approved"===a&&"approved"!==E){let{addTransaction:t}=await Promise.resolve().then(e.bind(e,3592));await t(l,{type:"withdrawal_approved",amount:0,description:"Withdrawal approved - ₹".concat(c," processed for transfer")})}if("rejected"===a&&"rejected"!==E){let{updateWalletBalance:t,addTransaction:a}=await Promise.resolve().then(e.bind(e,3592));await t(l,c),await a(l,{type:"withdrawal_rejected",amount:c,description:"Withdrawal rejected - ₹".concat(c," credited back to wallet")})}n.delete("dashboard-stats")}catch(t){throw console.error("Error updating withdrawal status:",t),t}}}}]);