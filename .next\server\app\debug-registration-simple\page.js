(()=>{var e={};e.id=4317,e.ids=[4317],e.modules={269:(e,r,t)=>{Promise.resolve().then(t.bind(t,81659))},643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},24237:(e,r,t)=>{Promise.resolve().then(t.bind(t,57565))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,r,t)=>{"use strict";t.d(r,{db:()=>u,j2:()=>l});var s=t(67989),i=t(63385),o=t(75535),a=t(70146);let n=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),l=(0,i.xI)(n),u=(0,o.aU)(n);(0,a.c7)(n)},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57565:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var s=t(60687),i=t(43210),o=t(63385),a=t(75535),n=t(33784),l=t(3582);function u(){let[e,r]=(0,i.useState)(""),[t,u]=(0,i.useState)(!1),[d,c]=(0,i.useState)({name:"Test User",email:"",mobile:"9876543210",password:"test123456",confirmPassword:"test123456",referralCode:""}),p=e=>{r(r=>r+e+"\n"),console.log(e)},m=e=>{let{name:r,value:t}=e.target;c(e=>({...e,[r]:t}))},x=async()=>{r(""),u(!0);try{let e,r=`test${Date.now()}@example.com`,t="Test Registration User",s="9876543210";p("\uD83D\uDE80 Starting registration test..."),p(`📧 Email: ${r}`),p(`👤 Name: ${t}`),p(`📱 Mobile: ${s}`),p(`🔧 Firebase Project: mytube-india`),p("\n=== STEP 1: Creating Firebase Auth User ===");try{let e=(await (0,o.eJ)(n.j2,r,"test123456")).user;p(`✅ Auth user created successfully!`),p(`🆔 UID: ${e.uid}`),p(`📧 Email: ${e.email}`),p(`✅ Email Verified: ${e.emailVerified}`)}catch(e){throw p(`❌ Auth creation failed: ${e.message}`),p(`❌ Auth error code: ${e.code}`),e}p("\n=== STEP 2: Waiting for Auth State ==="),await new Promise(e=>setTimeout(e,2e3)),p(`✅ Auth state propagated`),p(`Current auth user: ${n.j2.currentUser?.uid}`),p(`Auth state matches: ${n.j2.currentUser?.uid===n.j2.currentUser?.uid}`),p("\n=== STEP 3: Generating Referral Code ===");try{e=await (0,l.x4)(),p(`✅ Generated referral code: ${e}`)}catch(e){throw p(`❌ Referral code generation failed: ${e.message}`),e}p("\n=== STEP 4: Preparing User Data ===");let i={[l.FIELD_NAMES.name]:t,[l.FIELD_NAMES.email]:r.toLowerCase(),[l.FIELD_NAMES.mobile]:s,[l.FIELD_NAMES.referralCode]:e,[l.FIELD_NAMES.referredBy]:"",[l.FIELD_NAMES.referralBonusCredited]:!1,[l.FIELD_NAMES.plan]:"Trial",[l.FIELD_NAMES.planExpiry]:null,[l.FIELD_NAMES.activeDays]:0,[l.FIELD_NAMES.joinedDate]:a.Dc.now(),[l.FIELD_NAMES.wallet]:0,[l.FIELD_NAMES.totalVideos]:0,[l.FIELD_NAMES.todayVideos]:0,[l.FIELD_NAMES.lastVideoDate]:null,[l.FIELD_NAMES.videoDuration]:30,status:"active"};p(`✅ User data prepared`),p(`📊 Data keys: ${Object.keys(i).join(", ")}`),p("\n=== STEP 5: Creating Firestore Document ===");let u=n.j2.currentUser;if(!u)throw p(`❌ No current user found`),Error("No current user found");let d=(0,a.H9)(n.db,l.COLLECTIONS.users,u.uid);p(`📍 Document path: ${d.path}`),p(`🔐 Current user UID: ${u.uid}`),p(`📧 Current user email: ${u.email}`);try{await (0,a.BN)(d,i),p(`✅ Firestore document created successfully!`)}catch(e){throw p(`❌ Firestore creation failed: ${e.message}`),p(`❌ Firestore error code: ${e.code}`),p(`❌ Full error: ${JSON.stringify(e,null,2)}`),e}p("\n=== STEP 6: Verifying Document ===");try{let e=await (0,a.x7)(d);if(e.exists()){let r=e.data();p(`✅ Document verification successful!`),p(`📊 Document data keys: ${Object.keys(r).join(", ")}`),p(`👤 Name: ${r[l.FIELD_NAMES.name]}`),p(`📧 Email: ${r[l.FIELD_NAMES.email]}`),p(`🎯 Referral Code: ${r[l.FIELD_NAMES.referralCode]}`)}else throw p(`❌ Document was not created properly`),Error("Document verification failed")}catch(e){throw p(`❌ Document verification failed: ${e.message}`),e}p("\n\uD83C\uDF89 Registration test completed successfully!")}catch(e){p(`
❌ Registration test failed!`),p(`Error: ${e.message}`),p(`Code: ${e.code}`),p(`Stack: ${e.stack}`),console.error("Registration test error:",e)}finally{u(!1)}},f=async e=>{e.preventDefault(),r(""),u(!0);try{let e=d.email||`test${Date.now()}@example.com`;if(p("\uD83D\uDE80 Starting FORM registration test..."),p(`📧 Email: ${e}`),p(`👤 Name: ${d.name}`),p(`📱 Mobile: ${d.mobile}`),!d.name||!e||!d.mobile||!d.password)throw Error("Please fill in all required fields");if(d.password!==d.confirmPassword)throw Error("Passwords do not match");let r=(await (0,o.eJ)(n.j2,e,d.password)).user;p(`✅ Auth user created: ${r.uid}`);let t=await (0,l.x4)();p(`✅ Referral code: ${t}`);let s={[l.FIELD_NAMES.name]:d.name.trim(),[l.FIELD_NAMES.email]:e.toLowerCase(),[l.FIELD_NAMES.mobile]:d.mobile,[l.FIELD_NAMES.referralCode]:t,[l.FIELD_NAMES.referredBy]:d.referralCode||"",[l.FIELD_NAMES.referralBonusCredited]:!1,[l.FIELD_NAMES.plan]:"Trial",[l.FIELD_NAMES.planExpiry]:null,[l.FIELD_NAMES.activeDays]:0,[l.FIELD_NAMES.joinedDate]:a.Dc.now(),[l.FIELD_NAMES.wallet]:0,[l.FIELD_NAMES.totalVideos]:0,[l.FIELD_NAMES.todayVideos]:0,[l.FIELD_NAMES.lastVideoDate]:null,[l.FIELD_NAMES.videoDuration]:30,status:"active"},i=(0,a.H9)(n.db,l.COLLECTIONS.users,r.uid);await (0,a.BN)(i,s),p(`✅ Document created successfully!`),p("\n\uD83C\uDF89 FORM registration test completed successfully!")}catch(e){p(`
❌ FORM registration test failed!`),p(`Error: ${e.message}`),p(`Code: ${e.code}`)}finally{u(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-8",children:(0,s.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,s.jsxs)("div",{className:"glass-card p-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-white mb-6",children:"Automated Test"}),(0,s.jsx)("button",{onClick:x,disabled:t,className:"btn-primary mb-6 w-full",children:t?"Testing Registration...":"Test Registration Process"}),(0,s.jsx)("div",{className:"bg-black/30 rounded-lg p-4 text-white font-mono text-sm whitespace-pre-wrap max-h-96 overflow-y-auto",children:e||"Click the button to test registration process..."})]}),(0,s.jsxs)("div",{className:"glass-card p-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-white mb-6",children:"Form Test"}),(0,s.jsxs)("form",{onSubmit:f,className:"space-y-4 mb-6",children:[(0,s.jsx)("input",{type:"text",name:"name",value:d.name,onChange:m,placeholder:"Full Name",className:"form-input",required:!0}),(0,s.jsx)("input",{type:"email",name:"email",value:d.email,onChange:m,placeholder:"Email (leave empty for auto-generated)",className:"form-input"}),(0,s.jsx)("input",{type:"tel",name:"mobile",value:d.mobile,onChange:m,placeholder:"Mobile Number",className:"form-input",required:!0}),(0,s.jsx)("input",{type:"password",name:"password",value:d.password,onChange:m,placeholder:"Password",className:"form-input",required:!0}),(0,s.jsx)("input",{type:"password",name:"confirmPassword",value:d.confirmPassword,onChange:m,placeholder:"Confirm Password",className:"form-input",required:!0}),(0,s.jsx)("input",{type:"text",name:"referralCode",value:d.referralCode,onChange:m,placeholder:"Referral Code (Optional)",className:"form-input"}),(0,s.jsx)("button",{type:"submit",disabled:t,className:"btn-primary w-full",children:t?"Testing...":"Test Form Registration"})]}),(0,s.jsx)("div",{className:"bg-black/30 rounded-lg p-4 text-white font-mono text-sm whitespace-pre-wrap max-h-96 overflow-y-auto",children:e||"Fill the form and submit to test..."})]})]})})})}},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},73519:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>p,tree:()=>u});var s=t(65239),i=t(48088),o=t(88170),a=t.n(o),n=t(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(r,l);let u={children:["",{children:["debug-registration-simple",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,81659)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\debug-registration-simple\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\debug-registration-simple\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/debug-registration-simple/page",pathname:"/debug-registration-simple",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},81659:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\debug-registration-simple\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\debug-registration-simple\\page.tsx","default")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6204,2756,8441,3582],()=>t(73519));module.exports=s})();