(()=>{var e={};e.id=481,e.ids=[481],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21265:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(60687),i=r(43210),o=r(85814),a=r.n(o),n=r(87979),l=r(77567);let c=[{id:"trial",name:"Trial",price:0,duration:2,earningPerVideo:10,videoDuration:30,features:["2 days access","₹10 per batch (50 videos)","Basic support","Video duration: 30 seconds"]},{id:"starter",name:"Starter",price:499,duration:30,earningPerVideo:25,videoDuration:300,features:["30 days access","₹25 per batch (50 videos)","Priority support","Referral bonus: ₹50","Video duration: 5 minutes"]},{id:"basic",name:"Basic",price:1499,duration:30,earningPerVideo:75,videoDuration:300,features:["30 days access","₹75 per batch (50 videos)","Priority support","Referral bonus: ₹150","50 videos credited to total count","Video duration: 5 minutes"],popular:!0},{id:"premium",name:"Premium",price:2999,duration:30,earningPerVideo:150,videoDuration:300,features:["30 days access","₹150 per batch (50 videos)","Premium support","Referral bonus: ₹300","50 videos credited to total count","Video duration: 5 minutes"]},{id:"gold",name:"Gold",price:3999,duration:30,earningPerVideo:200,videoDuration:180,features:["30 days access","₹200 per batch (50 videos)","Premium support","Referral bonus: ₹400","50 videos credited to total count","Video duration: 3 minutes","Priority customer support"]},{id:"platinum",name:"Platinum",price:5999,duration:30,earningPerVideo:250,videoDuration:120,features:["30 days access","₹250 per batch (50 videos)","Premium support","Referral bonus: ₹700","50 videos credited to total count","Video duration: 2 minutes","Dedicated account manager","Early access to new features"]},{id:"diamond",name:"Diamond",price:9999,duration:30,earningPerVideo:400,videoDuration:60,features:["30 days access","₹400 per batch (50 videos)","VIP support","Referral bonus: ₹1200","50 videos credited to total count","Video duration: 1 minute","Dedicated account manager","Early access to new features","Exclusive earning opportunities"]}];function d(){let{user:e,loading:t}=(0,n.hD)(),[r,o]=(0,i.useState)(null),[d,u]=(0,i.useState)(!1),p=async t=>{if(!e)return void l.A.fire({icon:"info",title:"Login Required",text:"Please login to purchase a plan",showCancelButton:!0,confirmButtonText:"Login",cancelButtonText:"Cancel"}).then(e=>{e.isConfirmed&&(window.location.href="/login")});if("trial"===t.id)return void l.A.fire({icon:"info",title:"Trial Plan",text:"You are already on the trial plan. Upgrade to a paid plan for better earnings!"});o(t.id),u(!0);try{await new Promise(e=>setTimeout(e,2e3)),l.A.fire({icon:"info",title:"Payment Integration Required",html:`
          <p>To complete your purchase of the <strong>${t.name}</strong> plan (₹${t.price}), please contact our support team.</p>
          <br>
          <p><strong>Plan Details:</strong></p>
          <ul style="text-align: left; margin: 10px 0;">
            <li>Duration: ${t.duration} days</li>
            <li>Earning: ₹${t.earningPerVideo} per 50 videos</li>
            <li>Video duration: ${t.videoDuration<60?`${t.videoDuration} seconds`:`${Math.floor(t.videoDuration/60)} minute${t.videoDuration>=120?"s":""}`}</li>
          </ul>
          <br>
          <p><strong>Contact Options:</strong></p>
          <p>📧 Email: <strong><EMAIL></strong></p>
        `,confirmButtonText:"Contact Support",showCancelButton:!0,cancelButtonText:"Cancel"})}catch(e){console.error("Error processing plan selection:",e),l.A.fire({icon:"error",title:"Error",text:"Failed to process plan selection. Please try again."})}finally{u(!1),o(null)}};return t?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"spinner"})}):(0,s.jsxs)("div",{className:"min-h-screen p-4",children:[(0,s.jsx)("header",{className:"glass-card p-4 mb-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(a(),{href:e?"/dashboard":"/",className:"glass-button px-4 py-2 text-white",children:[(0,s.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back"]}),(0,s.jsx)("h1",{className:"text-xl font-bold text-white",children:"Choose Your Plan"}),(0,s.jsx)("div",{className:"w-20"})," "]})}),(0,s.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-white mb-4",children:"Start Earning with MyTube"}),(0,s.jsx)("p",{className:"text-white/80 text-lg max-w-2xl mx-auto",children:"Choose the perfect plan for your earning goals. Watch videos and earn money with our flexible pricing options."})]}),(0,s.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-6",children:c.map(e=>(0,s.jsxs)("div",{className:`glass-card p-8 relative ${e.popular?"ring-2 ring-yellow-400":""}`,children:[e.popular&&(0,s.jsx)("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2",children:(0,s.jsx)("span",{className:"bg-yellow-400 text-black px-4 py-1 rounded-full text-sm font-bold",children:"Most Popular"})}),(0,s.jsxs)("div",{className:"text-center mb-6",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:e.name}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("span",{className:"text-4xl font-bold text-white",children:["₹",e.price]}),e.price>0&&(0,s.jsxs)("span",{className:"text-white/60 ml-2",children:["/ ",e.duration," days"]})]}),(0,s.jsxs)("p",{className:"text-green-400 font-semibold",children:["Earn ₹",e.earningPerVideo," per 50 videos"]})]}),(0,s.jsx)("ul",{className:"space-y-3 mb-8",children:e.features.map((e,t)=>(0,s.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,s.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),e]},t))}),(0,s.jsx)("button",{onClick:()=>p(e),disabled:d&&r===e.id,className:`w-full py-3 rounded-lg font-semibold transition-all duration-300 ${e.popular?"bg-yellow-400 text-black hover:bg-yellow-500":0===e.price?"bg-gray-600 text-white hover:bg-gray-700":"bg-youtube-red text-white hover:bg-red-700"} disabled:opacity-50`,children:d&&r===e.id?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-5 h-5 inline-block"}),"Processing..."]}):0===e.price?"Current Plan":`Choose ${e.name}`})]},e.id))}),(0,s.jsxs)("div",{className:"mt-12 glass-card p-8",children:[(0,s.jsxs)("h3",{className:"text-xl font-bold text-white mb-4 text-center",children:[(0,s.jsx)("i",{className:"fas fa-info-circle mr-2"}),"Plan Benefits Explained"]}),(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-3",children:"Earning Structure"}),(0,s.jsxs)("ul",{className:"space-y-2 text-white/80",children:[(0,s.jsx)("li",{children:"• Watch 50 videos daily to earn the full amount"}),(0,s.jsx)("li",{children:"• Each video must be watched for the full duration"}),(0,s.jsx)("li",{children:"• Earnings are credited to your earning wallet"}),(0,s.jsx)("li",{children:"• Transfer earnings to main wallet for withdrawal"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-3",children:"Video Duration Benefits"}),(0,s.jsxs)("ul",{className:"space-y-2 text-white/80",children:[(0,s.jsx)("li",{children:"• Higher plans have shorter video durations"}),(0,s.jsx)("li",{children:"• Complete daily targets faster with premium plans"}),(0,s.jsx)("li",{children:"• Video duration is fixed per plan"}),(0,s.jsx)("li",{children:"• All videos must be watched completely"})]})]})]})]}),(0,s.jsxs)("div",{className:"mt-8 text-center",children:[(0,s.jsx)("p",{className:"text-white/60 mb-4",children:"Need help choosing a plan? Contact us during business hours (9 AM - 6 PM, working days):"}),(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsxs)("a",{href:"mailto:<EMAIL>",className:"flex items-center text-white hover:text-blue-400 transition-colors",children:[(0,s.jsx)("i",{className:"fas fa-envelope mr-2"}),"<EMAIL>"]})})]})]})]})}},21820:e=>{"use strict";e.exports=require("os")},26147:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=r(65239),i=r(48088),o=r(88170),a=r.n(o),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let c={children:["",{children:["plans",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,41587)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\plans\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\plans\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/plans/page",pathname:"/plans",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,t,r)=>{"use strict";r.d(t,{db:()=>c,j2:()=>l});var s=r(67989),i=r(63385),o=r(75535),a=r(70146);let n=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),l=(0,i.xI)(n),c=(0,o.aU)(n);(0,a.c7)(n)},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41587:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\plans\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\plans\\page.tsx","default")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},51278:(e,t,r)=>{"use strict";r.d(t,{M4:()=>n,_f:()=>a});var s=r(33784),i=r(77567);function o(e){try{Object.keys(localStorage).forEach(t=>{(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function a(e,t="/login"){try{if((await i.A.fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&o(e),await s.j2.signOut(),i.A.fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),i.A.fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function n(e,t="/login"){try{e&&o(e),await s.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85613:(e,t,r)=>{Promise.resolve().then(r.bind(r,21265))},87821:(e,t,r)=>{Promise.resolve().then(r.bind(r,41587))},87979:(e,t,r)=>{"use strict";r.d(t,{Nu:()=>a,hD:()=>o,wC:()=>n});var s=r(43210);r(63385),r(33784);var i=r(51278);function o(){let[e,t]=(0,s.useState)(null),[r,o]=(0,s.useState)(!0),a=async()=>{try{await (0,i.M4)(e?.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:r,signOut:a}}function a(){let{user:e,loading:t}=o();return{user:e,loading:t}}function n(){let{user:e,loading:t}=o(),[r,i]=(0,s.useState)(!1),[a,n]=(0,s.useState)(!0);return{user:e,loading:t||a,isAdmin:r}}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[6204,2756,7567,8441],()=>r(26147));module.exports=s})();