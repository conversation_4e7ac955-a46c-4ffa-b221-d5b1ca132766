(()=>{var e={};e.id=139,e.ids=[139],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16611:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>p,tree:()=>l});var s=t(65239),i=t(48088),o=t(88170),a=t.n(o),n=t(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(r,c);let l={children:["",{children:["registration-diagnostics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,67079)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\registration-diagnostics\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\registration-diagnostics\\page.tsx"],d={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/registration-diagnostics/page",pathname:"/registration-diagnostics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,r,t)=>{"use strict";t.d(r,{db:()=>l,j2:()=>c});var s=t(67989),i=t(63385),o=t(75535),a=t(70146);let n=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),c=(0,i.xI)(n),l=(0,o.aU)(n);(0,a.c7)(n)},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67079:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\registration-diagnostics\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\registration-diagnostics\\page.tsx","default")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77001:(e,r,t)=>{Promise.resolve().then(t.bind(t,86049))},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},86049:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l});var s=t(60687),i=t(43210),o=t(63385),a=t(75535),n=t(33784),c=t(3582);function l(){let[e,r]=(0,i.useState)([]),[t,l]=(0,i.useState)(!1),[u,d]=(0,i.useState)(""),p=(e,t="info")=>{let s=new Date().toLocaleTimeString(),i=`[${s}] ${"success"===t?"✅":"error"===t?"❌":"warning"===t?"⚠️":"ℹ️"} ${e}`;r(e=>[...e,i]),console.log(i)},m=()=>{r([])},x=async()=>{l(!0),m();try{let e=u||`diagnostic${Date.now()}@test.com`;p("\uD83D\uDE80 Starting Registration Diagnostics","info"),p(`📧 Test Email: ${e}`),p(`🔧 Firebase Project: mytube-india`),p("\n=== STEP 1: Firebase Configuration Check ==="),p("API Key: Present"),p("Auth Domain: mytube-india.firebaseapp.com"),p("Project ID: mytube-india"),p("App ID: Present"),p("\n=== STEP 2: Firebase Auth Test ===");let r=null;try{p("Creating Firebase Auth user..."),r=(await (0,o.eJ)(n.j2,e,"test123456")).user,p(`Auth user created successfully: ${r.uid}`,"success"),p(`User email: ${r.email}`),p(`Email verified: ${r.emailVerified}`)}catch(e){throw p(`Auth creation failed: ${e.message}`,"error"),p(`Auth error code: ${e.code}`,"error"),e}p("\n=== STEP 3: Auth State Propagation ==="),p("Waiting 2 seconds for auth state to propagate..."),await new Promise(e=>setTimeout(e,2e3)),p(`Current auth user: ${n.j2.currentUser?.uid}`),p(`Auth state matches: ${n.j2.currentUser?.uid===r.uid}`,n.j2.currentUser?.uid===r.uid?"success":"warning"),p("\n=== STEP 4: Referral Code Generation ===");let t="";try{p("Generating referral code..."),t=await (0,c.generateUniqueReferralCode)(),p(`Referral code generated: ${t}`,"success")}catch(e){p(`Referral code generation failed: ${e.message}`,"error"),p("Using fallback referral code...","warning"),t=`MYN${Date.now().toString().slice(-4)}`,p(`Fallback referral code: ${t}`)}p("\n=== STEP 5: User Data Preparation ===");let s={[c.FIELD_NAMES.name]:"Diagnostic Test User",[c.FIELD_NAMES.email]:e.toLowerCase(),[c.FIELD_NAMES.mobile]:"9876543210",[c.FIELD_NAMES.referralCode]:t,[c.FIELD_NAMES.referredBy]:"",[c.FIELD_NAMES.referralBonusCredited]:!1,[c.FIELD_NAMES.plan]:"Trial",[c.FIELD_NAMES.planExpiry]:null,[c.FIELD_NAMES.activeDays]:0,[c.FIELD_NAMES.joinedDate]:a.Dc.now(),[c.FIELD_NAMES.wallet]:0,[c.FIELD_NAMES.totalVideos]:0,[c.FIELD_NAMES.todayVideos]:0,[c.FIELD_NAMES.lastVideoDate]:null,[c.FIELD_NAMES.videoDuration]:30,status:"active"};p(`User data prepared with ${Object.keys(s).length} fields`,"success"),p(`Data fields: ${Object.keys(s).join(", ")}`),p("\n=== STEP 6: Firestore Document Creation ===");try{let e=(0,a.H9)(n.db,c.COLLECTIONS.users,r.uid);p(`Document path: ${e.path}`),p(`Collection: ${c.COLLECTIONS.users}`),p(`Document ID: ${r.uid}`),p("Attempting to create Firestore document..."),await (0,a.BN)(e,s),p("Firestore document created successfully!","success")}catch(e){throw p(`Firestore creation failed: ${e.message}`,"error"),p(`Firestore error code: ${e.code}`,"error"),p(`Full error: ${JSON.stringify(e,null,2)}`,"error"),e}p("\n=== STEP 7: Document Verification ===");try{let e=(0,a.H9)(n.db,c.COLLECTIONS.users,r.uid),t=await (0,a.x7)(e);if(t.exists()){let e=t.data();p("Document verification successful!","success"),p(`Retrieved ${Object.keys(e).length} fields`),p(`Name: ${e[c.FIELD_NAMES.name]}`),p(`Email: ${e[c.FIELD_NAMES.email]}`),p(`Plan: ${e[c.FIELD_NAMES.plan]}`)}else throw p("Document does not exist after creation!","error"),Error("Document verification failed")}catch(e){throw p(`Document verification failed: ${e.message}`,"error"),e}p("\n\uD83C\uDF89 All diagnostics passed! Registration should work.","success")}catch(e){p(`
💥 Diagnostics failed at: ${e.message}`,"error"),p(`Error code: ${e.code||"N/A"}`,"error"),p(`Error stack: ${e.stack}`,"error")}finally{l(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-8",children:(0,s.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,s.jsxs)("div",{className:"glass-card p-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-white mb-6",children:"Registration Diagnostics"}),(0,s.jsxs)("div",{className:"mb-6 space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"Test Email (optional - will auto-generate if empty)"}),(0,s.jsx)("input",{type:"email",value:u,onChange:e=>d(e.target.value),placeholder:"<EMAIL>",className:"form-input"})]}),(0,s.jsxs)("div",{className:"flex space-x-4",children:[(0,s.jsx)("button",{onClick:x,disabled:t,className:"btn-primary",children:t?"Running Diagnostics...":"Run Registration Diagnostics"}),(0,s.jsx)("button",{onClick:m,disabled:t,className:"btn-secondary",children:"Clear Logs"})]})]}),(0,s.jsx)("div",{className:"bg-black/30 rounded-lg p-4 max-h-96 overflow-y-auto",children:(0,s.jsx)("div",{className:"text-white font-mono text-sm space-y-1",children:0===e.length?(0,s.jsx)("div",{className:"text-white/60",children:'Click "Run Registration Diagnostics" to start...'}):e.map((e,r)=>(0,s.jsx)("div",{className:"whitespace-pre-wrap",children:e},r))})}),(0,s.jsxs)("div",{className:"mt-6 p-4 bg-blue-500/20 rounded-lg",children:[(0,s.jsx)("h3",{className:"text-white font-bold mb-2",children:"What this test does:"}),(0,s.jsxs)("ul",{className:"text-white/80 text-sm space-y-1",children:[(0,s.jsx)("li",{children:"• Checks Firebase configuration"}),(0,s.jsx)("li",{children:"• Tests Firebase Auth user creation"}),(0,s.jsx)("li",{children:"• Verifies auth state propagation"}),(0,s.jsx)("li",{children:"• Tests referral code generation"}),(0,s.jsx)("li",{children:"• Attempts Firestore document creation"}),(0,s.jsx)("li",{children:"• Verifies document was created successfully"})]})]}),(0,s.jsxs)("div",{className:"mt-4 space-x-4",children:[(0,s.jsx)("a",{href:"/register",className:"btn-primary inline-block",children:"Go to Registration"}),(0,s.jsx)("a",{href:"/debug-registration-simple",className:"btn-secondary inline-block",children:"Debug Registration"})]})]})})})}},86729:(e,r,t)=>{Promise.resolve().then(t.bind(t,67079))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6204,2756,8441,3582],()=>t(16611));module.exports=s})();