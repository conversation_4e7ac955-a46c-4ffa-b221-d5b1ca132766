(()=>{var e={};e.id=4831,e.ids=[4831],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29047:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>p,tree:()=>u});var s=r(65239),i=r(48088),o=r(88170),n=r.n(o),a=r(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let u={children:["",{children:["test-registration",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,88433)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\test-registration\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\test-registration\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/test-registration/page",pathname:"/test-registration",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,t,r)=>{"use strict";r.d(t,{db:()=>u,j2:()=>l});var s=r(67989),i=r(63385),o=r(75535),n=r(70146);let a=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),l=(0,i.xI)(a),u=(0,o.aU)(a);(0,n.c7)(a)},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},42203:(e,t,r)=>{Promise.resolve().then(r.bind(r,55795))},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},55795:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(60687),i=r(43210),o=r(63385),n=r(75535),a=r(33784),l=r(3582);function u(){let[e,t]=(0,i.useState)(""),[r,u]=(0,i.useState)(!1),d=async()=>{t(""),u(!0);try{t(e=>e+"Testing referral code generation...\n");let e=await (0,l.pu)();t(t=>t+`✓ Referral code generated: ${e}
`);for(let e=0;e<3;e++){let r=await (0,l.pu)();t(t=>t+`✓ Code ${e+2}: ${r}
`)}}catch(e){t(t=>t+`✗ Error: ${e.message}
`),console.error("Test error:",e)}finally{u(!1)}},c=async()=>{t(""),u(!0);try{t(e=>e+"Testing Firestore write...\n");let e={name:"Test User",email:"<EMAIL>",mobile:"9876543210",referralCode:"TEST001",referredBy:"",plan:"Trial",planExpiry:null,activeDays:2,joinedDate:n.Dc.now(),wallet:0,totalVideos:0,todayVideos:0,lastVideoDate:null,status:"active"},r=`test_${Date.now()}`,s=(0,n.H9)(a.db,l.COLLECTIONS.users,r);t(e=>e+`Creating document: ${s.path}
`),await (0,n.BN)(s,e),t(e=>e+"✓ Document created successfully\n");let i=await (0,n.x7)(s);i.exists()?(t(e=>e+"✓ Document verified successfully\n"),t(e=>e+`Data: ${JSON.stringify(i.data(),null,2)}
`)):t(e=>e+"✗ Document not found after creation\n")}catch(e){t(t=>t+`✗ Error: ${e.message}
`),console.error("Test error:",e)}finally{u(!1)}},p=async()=>{t(""),u(!0);try{t(e=>e+"Testing full registration flow...\n");let e=`test_${Date.now()}@example.com`;t(t=>t+`Creating auth user: ${e}
`);let r=(await (0,o.eJ)(a.j2,e,"test123456")).user;t(e=>e+`✓ Auth user created: ${r.uid}
`),t(e=>e+"Generating referral code...\n");let s=await (0,l.pu)();t(e=>e+`✓ Referral code: ${s}
`);let i={[l.FIELD_NAMES.name]:"Test Registration User",[l.FIELD_NAMES.email]:e,[l.FIELD_NAMES.mobile]:"9876543210",[l.FIELD_NAMES.referralCode]:s,[l.FIELD_NAMES.referredBy]:"",[l.FIELD_NAMES.plan]:"Trial",[l.FIELD_NAMES.planExpiry]:null,[l.FIELD_NAMES.activeDays]:0,[l.FIELD_NAMES.joinedDate]:n.Dc.now(),[l.FIELD_NAMES.wallet]:0,[l.FIELD_NAMES.totalVideos]:0,[l.FIELD_NAMES.todayVideos]:0,[l.FIELD_NAMES.lastVideoDate]:null,status:"active"};t(e=>e+"Creating Firestore document...\n");let u=(0,n.H9)(a.db,l.COLLECTIONS.users,r.uid);await (0,n.BN)(u,i),t(e=>e+"✓ Firestore document created\n"),(await (0,n.x7)(u)).exists()?(t(e=>e+"✓ Document verified successfully\n"),t(e=>e+`✓ Full registration test completed successfully!
`)):t(e=>e+"✗ Document verification failed\n"),t(e=>e+"Cleaning up test user...\n"),await r.delete(),t(e=>e+"✓ Test user deleted\n")}catch(e){t(t=>t+`✗ Error: ${e.message}
`),console.error("Test error:",e)}finally{u(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-pink-900 p-4",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-white mb-4",children:"Registration Debug Tests"}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-4 mb-6",children:[(0,s.jsx)("button",{onClick:d,disabled:r,className:"btn-primary",children:"Test Referral Code Generation"}),(0,s.jsx)("button",{onClick:c,disabled:r,className:"btn-primary",children:"Test Firestore Write"}),(0,s.jsx)("button",{onClick:p,disabled:r,className:"btn-primary",children:"Test Full Registration"})]}),r&&(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)("div",{className:"spinner mr-2"}),(0,s.jsx)("span",{className:"text-white",children:"Running test..."})]}),(0,s.jsx)("div",{className:"bg-black/50 rounded-lg p-4 min-h-[200px]",children:(0,s.jsx)("pre",{className:"text-green-400 text-sm whitespace-pre-wrap font-mono",children:e||"Click a test button to start..."})})]}),(0,s.jsxs)("div",{className:"glass-card p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-white mb-4",children:"Debug Information"}),(0,s.jsxs)("div",{className:"text-white/80 space-y-2",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Firebase Project:"})," ","mytube-india"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Auth Domain:"})," ","mytube-india.firebaseapp.com"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Collections.users:"})," ",l.COLLECTIONS.users]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Field Names:"})," ",JSON.stringify(l.FIELD_NAMES,null,2)]})]})]})]})})}},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76547:(e,t,r)=>{Promise.resolve().then(r.bind(r,88433))},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},88433:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-registration\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\test-registration\\page.tsx","default")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[6204,2756,8441,3582],()=>r(29047));module.exports=s})();