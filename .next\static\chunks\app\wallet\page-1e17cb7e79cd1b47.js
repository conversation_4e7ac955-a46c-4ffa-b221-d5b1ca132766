(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1730,9567],{4732:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>h});var s=t(5155),l=t(2115),r=t(6874),n=t.n(r),i=t(6681),o=t(7460),c=t(6572),d=t(3592),m=t(8647),u=t(4752),x=t.n(u);function h(){let{user:e,loading:a}=(0,i.Nu)(),{hasBlockingNotifications:t,isChecking:r,markAllAsRead:u}=(0,o.J)((null==e?void 0:e.uid)||null),{isBlocked:h,leaveStatus:b}=(0,c.l)({userId:(null==e?void 0:e.uid)||null,checkInterval:3e4,enabled:!!e}),[w,f]=(0,l.useState)(null),[v,p]=(0,l.useState)([]),[g,N]=(0,l.useState)(!0),[j,y]=(0,l.useState)(""),[k,D]=(0,l.useState)(!1),[S,C]=(0,l.useState)(null),[E,A]=(0,l.useState)(!1),[L,M]=(0,l.useState)({accountHolderName:"",accountNumber:"",ifscCode:"",bankName:""}),[P,I]=(0,l.useState)(!1),[F,W]=(0,l.useState)({allowed:!0}),[_,B]=(0,l.useState)(!1),[q,H]=(0,l.useState)(null);(0,l.useEffect)(()=>{e&&(U(),J(),G(),T())},[e]),(0,l.useEffect)(()=>{h?W({allowed:!1,reason:b.reason||"Withdrawals are not available due to leave."}):e&&T()},[h,b,e]);let G=async()=>{try{let a=await (0,d.getUserData)(e.uid);H(a)}catch(e){console.error("Error loading user data:",e)}},T=async()=>{if(e)try{B(!0);let a=await (0,d.QD)(e.uid);W(a)}catch(e){console.error("Error checking withdrawal eligibility:",e),W({allowed:!1,reason:"Unable to verify withdrawal eligibility. Please try again."})}finally{B(!1)}},U=async()=>{try{N(!0);let[a,t]=await Promise.all([(0,d.k6)(e.uid),(0,d.i8)(e.uid,20)]);f(a);let s=t.map(e=>({id:e.id,type:"withdrawal",amount:-e.amount,description:"Withdrawal request - ₹".concat(e.amount),date:e.date,status:e.status}));p(s)}catch(e){console.error("Error loading wallet data:",e),x().fire({icon:"error",title:"Error",text:"Failed to load wallet data. Please try again."})}finally{N(!1)}},J=async()=>{try{let a=await (0,d.zb)(e.uid);C(a),a&&M(a)}catch(e){console.error("Error loading bank details:",e)}},O=async a=>{if(a.preventDefault(),!P)try{I(!0),await (0,d.mm)(e.uid,L),C(L),A(!1),x().fire({icon:"success",title:"Bank Details Saved",text:"Your bank details have been saved successfully",timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error saving bank details:",e),x().fire({icon:"error",title:"Error",text:e.message||"Failed to save bank details. Please try again."})}finally{I(!1)}},R=e=>{let{name:a,value:t}=e.target;M(e=>({...e,[a]:t}))},Y=async()=>{if(k)return;if(h)return void x().fire({icon:"warning",title:"Withdrawal Not Available",text:b.reason||"Withdrawals are not available due to leave.",confirmButtonText:"OK"});let a=parseFloat(j);if(!a||a<=0)return void x().fire({icon:"error",title:"Invalid Amount",text:"Please enter a valid amount to withdraw"});if(a<50)return void x().fire({icon:"error",title:"Minimum Withdrawal",text:"Minimum withdrawal amount is ₹50"});if(a>((null==w?void 0:w.wallet)||0))return void x().fire({icon:"error",title:"Insufficient Balance",text:"You do not have enough balance in your wallet"});if(!S)return void x().fire({icon:"warning",title:"Bank Details Required",text:"Please add your bank details before making a withdrawal"});try{D(!0),await (0,d.xj)(e.uid,a,S),await U(),x().fire({icon:"success",title:"Withdrawal Request Submitted",text:"Your withdrawal request for ₹".concat(a," has been submitted and will be processed within 24-48 hours.")}),y("")}catch(e){console.error("Error processing withdrawal:",e),x().fire({icon:"error",title:"Withdrawal Failed",text:e.message||"Failed to process withdrawal request. Please try again."})}finally{D(!1)}},z=e=>null==e||isNaN(e)?"₹0.00":"₹".concat(e.toFixed(2));return a||g||r?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner mb-4"}),(0,s.jsx)("p",{className:"text-white",children:a?"Loading...":r?"Checking notifications...":"Loading wallet..."})]})}):t&&e?(0,s.jsx)(m.A,{userId:e.uid,onAllRead:u}):(0,s.jsxs)("div",{className:"min-h-screen p-4",children:[(0,s.jsx)("header",{className:"glass-card p-4 mb-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(n(),{href:"/dashboard",className:"glass-button px-4 py-2 text-white",children:[(0,s.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,s.jsx)("h1",{className:"text-xl font-bold text-white",children:"My Wallet"}),(0,s.jsxs)("button",{onClick:U,className:"glass-button px-4 py-2 text-white",children:[(0,s.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})}),(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("h3",{className:"text-2xl font-semibold text-white",children:"My Wallet"}),(0,s.jsx)("i",{className:"fas fa-wallet text-green-400 text-3xl"})]}),(0,s.jsx)("p",{className:"text-4xl font-bold text-green-400 mb-2",children:z((null==w?void 0:w.wallet)||0)}),(0,s.jsx)("p",{className:"text-white/60",children:"Total available balance"})]}),(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("h3",{className:"text-lg font-bold text-white",children:[(0,s.jsx)("i",{className:"fas fa-university mr-2"}),"Bank Details"]}),S&&!E&&(0,s.jsxs)("button",{onClick:()=>A(!0),className:"glass-button px-4 py-2 text-white",children:[(0,s.jsx)("i",{className:"fas fa-edit mr-2"}),"Edit"]})]}),S||E?E?(0,s.jsxs)("form",{onSubmit:O,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"Account Holder Name *"}),(0,s.jsx)("input",{type:"text",name:"accountHolderName",value:L.accountHolderName,onChange:R,className:"form-input",placeholder:"Enter account holder name",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"Bank Name *"}),(0,s.jsx)("input",{type:"text",name:"bankName",value:L.bankName,onChange:R,className:"form-input",placeholder:"Enter bank name",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"Account Number *"}),(0,s.jsx)("input",{type:"text",name:"accountNumber",value:L.accountNumber,onChange:R,className:"form-input",placeholder:"Enter account number",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"IFSC Code *"}),(0,s.jsx)("input",{type:"text",name:"ifscCode",value:L.ifscCode,onChange:R,className:"form-input",placeholder:"Enter IFSC code (e.g., SBIN0001234)",required:!0})]})]}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)("button",{type:"submit",disabled:P,className:"".concat(P?"btn-disabled cursor-not-allowed opacity-50":"btn-primary hover:bg-blue-600"),children:P?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Saving Bank Details..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-save mr-2"}),"Save Bank Details"]})}),(0,s.jsx)("button",{type:"button",onClick:()=>A(!1),className:"btn-secondary",children:"Cancel"})]})]}):(0,s.jsx)("div",{className:"bg-white/10 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-white/60 text-sm",children:"Account Holder"}),(0,s.jsx)("p",{className:"text-white font-medium",children:null==S?void 0:S.accountHolderName})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-white/60 text-sm",children:"Bank Name"}),(0,s.jsx)("p",{className:"text-white font-medium",children:null==S?void 0:S.bankName})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-white/60 text-sm",children:"Account Number"}),(0,s.jsxs)("p",{className:"text-white font-medium",children:["****",null==S?void 0:S.accountNumber.slice(-4)]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-white/60 text-sm",children:"IFSC Code"}),(0,s.jsx)("p",{className:"text-white font-medium",children:null==S?void 0:S.ifscCode})]})]})}):(0,s.jsxs)("div",{className:"text-center py-6",children:[(0,s.jsx)("i",{className:"fas fa-university text-white/30 text-4xl mb-4"}),(0,s.jsx)("p",{className:"text-white/60 mb-4",children:"No bank details added yet"}),(0,s.jsxs)("button",{onClick:()=>A(!0),className:"btn-primary",children:[(0,s.jsx)("i",{className:"fas fa-plus mr-2"}),"Add Bank Details"]})]})]}),(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("h3",{className:"text-lg font-bold text-white mb-4",children:[(0,s.jsx)("i",{className:"fas fa-money-bill-wave mr-2"}),"Withdraw Funds"]}),(null==q?void 0:q.plan)==="Trial"&&(0,s.jsxs)("div",{className:"mb-4 p-4 bg-red-500/20 border border-red-500/30 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center mb-2",children:[(0,s.jsx)("i",{className:"fas fa-lock text-red-400 mr-2"}),(0,s.jsx)("span",{className:"text-red-400 font-medium",children:"Trial Plan Restriction"})]}),(0,s.jsx)("p",{className:"text-white/80 text-sm mb-3",children:"Trial plan users cannot make withdrawals. Please upgrade to a paid plan to enable withdrawal functionality."}),(0,s.jsx)("div",{className:"flex gap-3",children:(0,s.jsxs)(n(),{href:"/plans",className:"btn-primary inline-block",children:[(0,s.jsx)("i",{className:"fas fa-arrow-up mr-2"}),"Upgrade Plan"]})})]}),(0,s.jsxs)("div",{className:"mb-4 p-4 bg-blue-500/20 border border-blue-500/30 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center mb-2",children:[(0,s.jsx)("i",{className:"fas fa-clock text-blue-400 mr-2"}),(0,s.jsx)("span",{className:"text-blue-400 font-medium",children:"Withdrawal Timings"})]}),(0,s.jsxs)("p",{className:"text-white/80 text-sm mb-2",children:["Withdrawals are only allowed between ",(0,s.jsx)("strong",{children:"10:00 AM to 6:00 PM"})," on non-leave days."]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("p",{className:"text-white/60 text-xs",children:["Current time: ",new Date().toLocaleTimeString()," | Status: ",F.allowed?(0,s.jsx)("span",{className:"text-green-400 font-medium",children:"✓ Available"}):(0,s.jsx)("span",{className:"text-red-400 font-medium",children:"✗ Not Available"})]}),(0,s.jsx)("button",{onClick:T,disabled:_,className:"text-blue-400 hover:text-blue-300 text-xs",children:_?(0,s.jsx)("div",{className:"spinner w-3 h-3"}):(0,s.jsx)("i",{className:"fas fa-sync-alt"})})]}),!F.allowed&&F.reason&&(0,s.jsxs)("p",{className:"text-red-400 text-sm mt-2",children:[(0,s.jsx)("i",{className:"fas fa-exclamation-triangle mr-1"}),F.reason]})]}),S?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,s.jsx)("input",{type:"number",value:j,onChange:e=>y(e.target.value),placeholder:"Enter amount to withdraw (Min: ₹50)",className:"form-input flex-1",min:"50",max:(null==w?void 0:w.wallet)||0}),(0,s.jsx)("button",{onClick:Y,disabled:k||!j||!F.allowed,className:"whitespace-nowrap ".concat(k?"btn-disabled cursor-not-allowed opacity-50":F.allowed&&j?"btn-primary hover:bg-blue-600":"btn-disabled cursor-not-allowed opacity-50"),children:k?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Processing Withdrawal..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-download mr-2"}),"Withdraw ₹",j||"0"]})})]}),(0,s.jsxs)("p",{className:"text-white/60 text-sm mt-2",children:["Available: ",z((null==w?void 0:w.wallet)||0)," | Minimum: ₹50"]})]}):(0,s.jsxs)("div",{className:"text-center py-6",children:[(0,s.jsx)("i",{className:"fas fa-exclamation-triangle text-yellow-400 text-3xl mb-4"}),(0,s.jsx)("p",{className:"text-white/60 mb-4",children:"Please add your bank details before making a withdrawal"}),(0,s.jsxs)("button",{onClick:()=>A(!0),className:"btn-primary",children:[(0,s.jsx)("i",{className:"fas fa-university mr-2"}),"Add Bank Details"]})]})]}),(0,s.jsxs)("div",{className:"glass-card p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("h3",{className:"text-lg font-bold text-white",children:[(0,s.jsx)("i",{className:"fas fa-money-bill-wave mr-2"}),"Withdrawal History"]}),(0,s.jsxs)("button",{onClick:U,className:"glass-button px-4 py-2 text-white",children:[(0,s.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]}),0===v.length?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("i",{className:"fas fa-money-bill-wave text-white/30 text-4xl mb-4"}),(0,s.jsx)("p",{className:"text-white/60 mb-2",children:"No withdrawal requests yet"}),(0,s.jsx)("p",{className:"text-white/40 text-sm",children:"Your withdrawal requests will appear here"})]}):(0,s.jsx)("div",{className:"space-y-3",children:v.map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 bg-white/10 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("i",{className:"fas fa-money-bill-wave text-red-400 mr-3"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-white font-medium",children:e.description}),(0,s.jsxs)("p",{className:"text-white/60 text-sm",children:[e.date.toLocaleDateString()," at ",e.date.toLocaleTimeString()]})]})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsx)("p",{className:"font-bold text-red-400",children:z(Math.abs(e.amount))}),(0,s.jsx)("span",{className:"text-xs px-2 py-1 rounded-full ".concat("pending"===e.status?"bg-yellow-500/20 text-yellow-400":"approved"===e.status?"bg-green-500/20 text-green-400":"rejected"===e.status?"bg-red-500/20 text-red-400":"completed"===e.status?"bg-blue-500/20 text-blue-400":"bg-gray-500/20 text-gray-400"),children:"pending"===e.status?"⏳ Pending":"approved"===e.status?"✅ Approved":"rejected"===e.status?"❌ Rejected":"completed"===e.status?"✅ Completed":e.status})]})]},e.id))})]})]})}},6034:(e,a,t)=>{Promise.resolve().then(t.bind(t,4732))},6572:(e,a,t)=>{"use strict";t.d(a,{l:()=>r});var s=t(2115),l=t(9567);function r(e){let{userId:a,checkInterval:t=3e4,enabled:r=!0}=e,[n,i]=(0,s.useState)({blocked:!1,lastChecked:new Date}),[o,c]=(0,s.useState)(!1),d=(0,s.useCallback)(async()=>{if(a&&r)try{c(!0);let e=await (0,l.q8)(a);return i({blocked:e.blocked,reason:e.reason,lastChecked:new Date}),e}catch(e){return console.error("Error checking leave status:",e),i(e=>({...e,lastChecked:new Date})),{blocked:!1}}finally{c(!1)}},[a,r]);return(0,s.useEffect)(()=>{a&&r&&d()},[a,r,d]),(0,s.useEffect)(()=>{if(!a||!r||t<=0)return;let e=setInterval(()=>{d()},t);return()=>clearInterval(e)},[a,r,t,d]),{leaveStatus:n,isChecking:o,checkLeaveStatus:d,isBlocked:n.blocked}}},9567:(e,a,t)=>{"use strict";t.d(a,{applyUserLeave:()=>m,cancelUserLeave:()=>x,createAdminLeave:()=>n,debugAdminLeaveStatus:()=>o,deleteAdminLeave:()=>c,getAdminLeaves:()=>i,getUserLeaves:()=>u,getUserMonthlyLeaveCount:()=>h,isAdminLeaveDay:()=>d,isUserOnLeave:()=>b,q8:()=>w});var s=t(6104),l=t(5317);let r={adminLeaves:"adminLeaves",userLeaves:"userLeaves"};async function n(e){try{return(await (0,l.gS)((0,l.rJ)(s.db,r.adminLeaves),{...e,date:l.Dc.fromDate(e.date),createdAt:l.Dc.now()})).id}catch(e){throw console.error("Error creating admin leave:",e),e}}async function i(){try{let e=(0,l.P)((0,l.rJ)(s.db,r.adminLeaves),(0,l.My)("date","asc")),a=(await (0,l.GG)(e)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate(),createdAt:e.data().createdAt.toDate()}));return console.log("\uD83D\uDCC5 All admin leaves:",a),a}catch(e){throw console.error("Error getting admin leaves:",e),e}}async function o(){try{let e=new Date;console.log("\uD83D\uDD0D Debug: Checking admin leave status for today:",e.toDateString());let a=await d(e);console.log("\uD83D\uDCCA Debug: Admin leave result:",a);let t=await i();console.log("\uD83D\uDCC5 Debug: All admin leaves in database:",t);let s=t.filter(a=>a.date.toDateString()===e.toDateString());console.log("\uD83D\uDCC5 Debug: Today's admin leaves:",s)}catch(e){console.error("❌ Debug: Error checking admin leave status:",e)}}async function c(e){try{await (0,l.kd)((0,l.H9)(s.db,r.adminLeaves,e))}catch(e){throw console.error("Error deleting admin leave:",e),e}}async function d(e){try{let a=new Date(e);a.setHours(0,0,0,0);let t=new Date(e);t.setHours(23,59,59,999),console.log("\uD83D\uDD0D Checking admin leave for date range:",a.toISOString(),"to",t.toISOString());let n=(0,l.P)((0,l.rJ)(s.db,r.adminLeaves),(0,l._M)("date",">=",l.Dc.fromDate(a)),(0,l._M)("date","<=",l.Dc.fromDate(t))),i=await (0,l.GG)(n),o=!i.empty;return o?console.log("\uD83D\uDCC5 Found admin leave(s) for today:",i.docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate()}))):console.log("\uD83D\uDCC5 No admin leaves found for today"),o}catch(e){return console.error("❌ Error checking admin leave day:",e),!1}}async function m(e){try{let a,t,n,i=new Date,o=i.getFullYear(),c=i.getMonth()+1,d=await h(e.userId,o,c),m="pending";return d<4&&(m="approved",a="system",n=l.Dc.now(),t="Auto-approved: ".concat(d+1,"/").concat(4," monthly leaves used")),{id:(await (0,l.gS)((0,l.rJ)(s.db,r.userLeaves),{...e,date:l.Dc.fromDate(e.date),status:m,appliedAt:l.Dc.now(),...a&&{reviewedBy:a},...n&&{reviewedAt:n},...t&&{reviewNotes:t}})).id,autoApproved:"approved"===m,usedLeaves:d+ +("approved"===m),maxLeaves:4}}catch(e){throw console.error("Error applying user leave:",e),e}}async function u(e){try{let a=(0,l.P)((0,l.rJ)(s.db,r.userLeaves),(0,l._M)("userId","==",e),(0,l.My)("date","desc"));return(await (0,l.GG)(a)).docs.map(e=>{var a;return{id:e.id,...e.data(),date:e.data().date.toDate(),appliedAt:e.data().appliedAt.toDate(),reviewedAt:null==(a=e.data().reviewedAt)?void 0:a.toDate()}})}catch(e){throw console.error("Error getting user leaves:",e),e}}async function x(e){try{await (0,l.kd)((0,l.H9)(s.db,r.userLeaves,e))}catch(e){throw console.error("Error cancelling user leave:",e),e}}async function h(e,a,t){try{let n=new Date(a,t-1,1),i=new Date(a,t,0,23,59,59,999),o=(0,l.P)((0,l.rJ)(s.db,r.userLeaves),(0,l._M)("userId","==",e),(0,l._M)("status","==","approved"),(0,l._M)("date",">=",l.Dc.fromDate(n)),(0,l._M)("date","<=",l.Dc.fromDate(i)));return(await (0,l.GG)(o)).size}catch(e){return console.error("Error getting user monthly leave count:",e),0}}async function b(e,a){try{let t=new Date(a);t.setHours(0,0,0,0);let n=new Date(a);n.setHours(23,59,59,999),console.log("\uD83D\uDD0D Checking user leave for user:",e,"on date range:",t.toISOString(),"to",n.toISOString());let i=(0,l.P)((0,l.rJ)(s.db,r.userLeaves),(0,l._M)("userId","==",e),(0,l._M)("status","==","approved"),(0,l._M)("date",">=",l.Dc.fromDate(t)),(0,l._M)("date","<=",l.Dc.fromDate(n))),o=await (0,l.GG)(i),c=!o.empty;return c?console.log("\uD83D\uDC64 Found user leave(s) for today:",o.docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate()}))):console.log("\uD83D\uDC64 No user leaves found for today"),c}catch(e){return console.error("❌ Error checking user leave day:",e),!1}}async function w(e){try{let a=new Date;console.log("\uD83D\uDD0D Checking work block status for user:",e,"on date:",a.toDateString());try{let e=await d(a);if(console.log("\uD83D\uDCC5 Admin leave check result:",e),e)return console.log("\uD83D\uDEAB Work blocked due to admin leave"),{blocked:!0,reason:"System maintenance/holiday"}}catch(e){console.error("❌ Error checking admin leave (allowing work to continue):",e)}try{let t=await b(e,a);if(console.log("\uD83D\uDC64 User leave check result:",t),t)return console.log("\uD83D\uDEAB Work blocked due to user leave"),{blocked:!0,reason:"You are on approved leave today"}}catch(e){console.error("❌ Error checking user leave (allowing work to continue):",e)}return console.log("✅ Work is not blocked"),{blocked:!1}}catch(e){return console.error("❌ Error checking work block status (allowing work to continue):",e),{blocked:!1}}}}},e=>{var a=a=>e(e.s=a);e.O(0,[2992,7416,8320,5181,6874,3592,1018,8441,1684,7358],()=>a(6034)),_N_E=e.O()}]);