"use strict";exports.id=1391,exports.ids=[1391],exports.modules={91391:(t,a,e)=>{e.d(a,{Ki:()=>l,Pn:()=>i,TK:()=>w,getWithdrawals:()=>c,hG:()=>E,lo:()=>n,updateWithdrawalStatus:()=>h});var r=e(75535),d=e(33784),o=e(3582);let s=new Map;async function i(){let t="dashboard-stats",a=function(t){let a=s.get(t);return a&&Date.now()-a.timestamp<3e5?a.data:null}(t);if(a)return a;try{let a=new Date;a.setHours(0,0,0,0);let e=r.Dc.fromDate(a),i=await (0,r.GG)((0,r.rJ)(d.db,o.COLLECTIONS.users)),n=i.size,l=(0,r.P)((0,r.rJ)(d.db,o.COLLECTIONS.users),(0,r._M)(o.FIELD_NAMES.joinedDate,">=",e)),c=(await (0,r.GG)(l)).size,w=0,E=0,h=0,u=0;i.forEach(t=>{let e=t.data();w+=e[o.FIELD_NAMES.totalVideos]||0,E+=e[o.FIELD_NAMES.wallet]||0;let r=e[o.FIELD_NAMES.lastVideoDate]?.toDate();r&&r.toDateString()===a.toDateString()&&(h+=e[o.FIELD_NAMES.todayVideos]||0)});try{let t=(0,r.P)((0,r.rJ)(d.db,o.COLLECTIONS.transactions),(0,r._M)(o.FIELD_NAMES.type,"==","video_earning"),(0,r.AB)(1e3));(await (0,r.GG)(t)).forEach(t=>{let e=t.data(),r=e[o.FIELD_NAMES.date]?.toDate();r&&r>=a&&(u+=e[o.FIELD_NAMES.amount]||0)})}catch(t){console.warn("Could not fetch today's transactions:",t)}let L=(0,r.P)((0,r.rJ)(d.db,o.COLLECTIONS.withdrawals),(0,r._M)("status","==","pending")),D=(await (0,r.GG)(L)).size,p=(0,r.P)((0,r.rJ)(d.db,o.COLLECTIONS.withdrawals),(0,r._M)("date",">=",e)),S=(await (0,r.GG)(p)).size,y={totalUsers:n,totalVideos:w,totalEarnings:E,pendingWithdrawals:D,todayUsers:c,todayVideos:h,todayEarnings:u,todayWithdrawals:S};return s.set(t,{data:y,timestamp:Date.now()}),y}catch(t){throw console.error("Error getting admin dashboard stats:",t),t}}async function n(t=50,a=null){try{let e=(0,r.P)((0,r.rJ)(d.db,o.COLLECTIONS.users),(0,r.My)(o.FIELD_NAMES.joinedDate,"desc"),(0,r.AB)(t));a&&(e=(0,r.P)((0,r.rJ)(d.db,o.COLLECTIONS.users),(0,r.My)(o.FIELD_NAMES.joinedDate,"desc"),(0,r.HM)(a),(0,r.AB)(t)));let s=await (0,r.GG)(e);return{users:s.docs.map(t=>({id:t.id,...t.data(),joinedDate:t.data()[o.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:t.data()[o.FIELD_NAMES.planExpiry]?.toDate()})),lastDoc:s.docs[s.docs.length-1]||null,hasMore:s.docs.length===t}}catch(t){throw console.error("Error getting users:",t),t}}async function l(t){try{let a=(0,r.P)((0,r.rJ)(d.db,o.COLLECTIONS.users),(0,r._M)(o.FIELD_NAMES.mobile,"==",t));return(await (0,r.GG)(a)).docs.map(t=>({id:t.id,...t.data(),joinedDate:t.data()[o.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:t.data()[o.FIELD_NAMES.planExpiry]?.toDate()}))}catch(t){throw console.error("Error searching users by mobile:",t),t}}async function c(t=50,a=null){try{let e=(0,r.P)((0,r.rJ)(d.db,o.COLLECTIONS.withdrawals),(0,r.My)("date","desc"),(0,r.AB)(t));a&&(e=(0,r.P)((0,r.rJ)(d.db,o.COLLECTIONS.withdrawals),(0,r.My)("date","desc"),(0,r.HM)(a),(0,r.AB)(t)));let s=await (0,r.GG)(e);return{withdrawals:s.docs.map(t=>({id:t.id,...t.data(),date:t.data().date?.toDate()})),lastDoc:s.docs[s.docs.length-1]||null,hasMore:s.docs.length===t}}catch(t){throw console.error("Error getting withdrawals:",t),t}}async function w(t,a){try{await (0,r.mZ)((0,r.H9)(d.db,o.COLLECTIONS.users,t),a),s.delete("dashboard-stats")}catch(t){throw console.error("Error updating user:",t),t}}async function E(t){try{await (0,r.kd)((0,r.H9)(d.db,o.COLLECTIONS.users,t)),s.delete("dashboard-stats")}catch(t){throw console.error("Error deleting user:",t),t}}async function h(t,a,i){try{let n=await (0,r.x7)((0,r.H9)(d.db,o.COLLECTIONS.withdrawals,t));if(!n.exists())throw Error("Withdrawal not found");let{userId:l,amount:c,status:w}=n.data(),E={status:a,updatedAt:r.Dc.now()};if(i&&(E.adminNotes=i),await (0,r.mZ)((0,r.H9)(d.db,o.COLLECTIONS.withdrawals,t),E),"approved"===a&&"approved"!==w){let{addTransaction:t}=await Promise.resolve().then(e.bind(e,3582));await t(l,{type:"withdrawal_approved",amount:0,description:`Withdrawal approved - ₹${c} processed for transfer`})}if("rejected"===a&&"rejected"!==w){let{updateWalletBalance:t,addTransaction:a}=await Promise.resolve().then(e.bind(e,3582));await t(l,c),await a(l,{type:"withdrawal_rejected",amount:c,description:`Withdrawal rejected - ₹${c} credited back to wallet`})}s.delete("dashboard-stats")}catch(t){throw console.error("Error updating withdrawal status:",t),t}}}};