"use strict";exports.id=1391,exports.ids=[1391],exports.modules={91391:(t,a,e)=>{e.d(a,{CF:()=>c,Pn:()=>i,TK:()=>u,getWithdrawals:()=>w,hG:()=>L,lo:()=>n,nQ:()=>E,updateWithdrawalStatus:()=>h,x5:()=>l});var r=e(75535),o=e(33784),s=e(3582);let d=new Map;async function i(){let t="dashboard-stats",a=function(t){let a=d.get(t);return a&&Date.now()-a.timestamp<3e5?a.data:null}(t);if(a)return a;try{let a=new Date;a.setHours(0,0,0,0);let e=r.Dc.fromDate(a),i=await (0,r.GG)((0,r.rJ)(o.db,s.COLLECTIONS.users)),n=i.size,l=(0,r.P)((0,r.rJ)(o.db,s.COLLECTIONS.users),(0,r._M)(s.FIELD_NAMES.joinedDate,">=",e)),c=(await (0,r.GG)(l)).size,E=0,w=0,u=0,L=0;i.forEach(t=>{let e=t.data();E+=e[s.FIELD_NAMES.totalVideos]||0,w+=e[s.FIELD_NAMES.wallet]||0;let r=e[s.FIELD_NAMES.lastVideoDate]?.toDate();r&&r.toDateString()===a.toDateString()&&(u+=e[s.FIELD_NAMES.todayVideos]||0)});try{let t=(0,r.P)((0,r.rJ)(o.db,s.COLLECTIONS.transactions),(0,r._M)(s.FIELD_NAMES.type,"==","video_earning"),(0,r.AB)(1e3));(await (0,r.GG)(t)).forEach(t=>{let e=t.data(),r=e[s.FIELD_NAMES.date]?.toDate();r&&r>=a&&(L+=e[s.FIELD_NAMES.amount]||0)})}catch(t){console.warn("Could not fetch today's transactions:",t)}let h=(0,r.P)((0,r.rJ)(o.db,s.COLLECTIONS.withdrawals),(0,r._M)("status","==","pending")),D=(await (0,r.GG)(h)).size,p=(0,r.P)((0,r.rJ)(o.db,s.COLLECTIONS.withdrawals),(0,r._M)("date",">=",e)),S=(await (0,r.GG)(p)).size,C={totalUsers:n,totalVideos:E,totalEarnings:w,pendingWithdrawals:D,todayUsers:c,todayVideos:u,todayEarnings:L,todayWithdrawals:S};return d.set(t,{data:C,timestamp:Date.now()}),C}catch(t){throw console.error("Error getting admin dashboard stats:",t),t}}async function n(t=50,a=null){try{let e=(0,r.P)((0,r.rJ)(o.db,s.COLLECTIONS.users),(0,r.My)(s.FIELD_NAMES.joinedDate,"desc"),(0,r.AB)(t));a&&(e=(0,r.P)((0,r.rJ)(o.db,s.COLLECTIONS.users),(0,r.My)(s.FIELD_NAMES.joinedDate,"desc"),(0,r.HM)(a),(0,r.AB)(t)));let d=await (0,r.GG)(e);return{users:d.docs.map(t=>({id:t.id,...t.data(),joinedDate:t.data()[s.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:t.data()[s.FIELD_NAMES.planExpiry]?.toDate()})),lastDoc:d.docs[d.docs.length-1]||null,hasMore:d.docs.length===t}}catch(t){throw console.error("Error getting users:",t),t}}async function l(t){try{if(!t||0===t.trim().length)return[];let a=t.toLowerCase().trim(),e=(0,r.P)((0,r.rJ)(o.db,s.COLLECTIONS.users),(0,r.My)(s.FIELD_NAMES.joinedDate,"desc"));return(await (0,r.GG)(e)).docs.map(t=>({id:t.id,...t.data(),joinedDate:t.data()[s.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:t.data()[s.FIELD_NAMES.planExpiry]?.toDate()})).filter(t=>{let e=(t[s.FIELD_NAMES.name]||"").toLowerCase(),r=(t[s.FIELD_NAMES.email]||"").toLowerCase(),o=(t[s.FIELD_NAMES.mobile]||"").toLowerCase(),d=(t[s.FIELD_NAMES.referralCode]||"").toLowerCase();return e.includes(a)||r.includes(a)||o.includes(a)||d.includes(a)})}catch(t){throw console.error("Error searching users:",t),t}}async function c(){try{let t=(0,r.P)((0,r.rJ)(o.db,s.COLLECTIONS.users),(0,r.My)(s.FIELD_NAMES.joinedDate,"desc"));return(await (0,r.GG)(t)).docs.map(t=>({id:t.id,...t.data(),joinedDate:t.data()[s.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:t.data()[s.FIELD_NAMES.planExpiry]?.toDate()}))}catch(t){throw console.error("Error getting all users:",t),t}}async function E(){try{let t=(0,r.P)((0,r.rJ)(o.db,s.COLLECTIONS.users));return(await (0,r.GG)(t)).size}catch(t){throw console.error("Error getting total user count:",t),t}}async function w(t=50,a=null){try{let e=(0,r.P)((0,r.rJ)(o.db,s.COLLECTIONS.withdrawals),(0,r.My)("date","desc"),(0,r.AB)(t));a&&(e=(0,r.P)((0,r.rJ)(o.db,s.COLLECTIONS.withdrawals),(0,r.My)("date","desc"),(0,r.HM)(a),(0,r.AB)(t)));let d=await (0,r.GG)(e);return{withdrawals:d.docs.map(t=>({id:t.id,...t.data(),date:t.data().date?.toDate()})),lastDoc:d.docs[d.docs.length-1]||null,hasMore:d.docs.length===t}}catch(t){throw console.error("Error getting withdrawals:",t),t}}async function u(t,a){try{await (0,r.mZ)((0,r.H9)(o.db,s.COLLECTIONS.users,t),a),d.delete("dashboard-stats")}catch(t){throw console.error("Error updating user:",t),t}}async function L(t){try{await (0,r.kd)((0,r.H9)(o.db,s.COLLECTIONS.users,t)),d.delete("dashboard-stats")}catch(t){throw console.error("Error deleting user:",t),t}}async function h(t,a,i){try{let n=await (0,r.x7)((0,r.H9)(o.db,s.COLLECTIONS.withdrawals,t));if(!n.exists())throw Error("Withdrawal not found");let{userId:l,amount:c,status:E}=n.data(),w={status:a,updatedAt:r.Dc.now()};if(i&&(w.adminNotes=i),await (0,r.mZ)((0,r.H9)(o.db,s.COLLECTIONS.withdrawals,t),w),"approved"===a&&"approved"!==E){let{addTransaction:t}=await Promise.resolve().then(e.bind(e,3582));await t(l,{type:"withdrawal_approved",amount:0,description:`Withdrawal approved - ₹${c} processed for transfer`})}if("rejected"===a&&"rejected"!==E){let{updateWalletBalance:t,addTransaction:a}=await Promise.resolve().then(e.bind(e,3582));await t(l,c),await a(l,{type:"withdrawal_rejected",amount:c,description:`Withdrawal rejected - ₹${c} credited back to wallet`})}d.delete("dashboard-stats")}catch(t){throw console.error("Error updating withdrawal status:",t),t}}}};