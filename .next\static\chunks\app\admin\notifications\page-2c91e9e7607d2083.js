(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[993,6779],{12:(e,t,a)=>{"use strict";a.d(t,{M4:()=>o,_f:()=>n});var s=a(6104),r=a(4752),i=a.n(r);function l(e){try{Object.keys(localStorage).forEach(t=>{(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function n(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await i().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&l(e),await s.j2.signOut(),i().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),i().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{e&&l(e),await s.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}},3737:(e,t,a)=>{"use strict";function s(e,t,a){if(!e||0===e.length)return void alert("No data to export");let s=a||Object.keys(e[0]),r=new Blob([[s.join(","),...e.map(e=>s.map(t=>{let a=e[t];if(null==a)return"";if("string"==typeof a){let e=a.replace(/"/g,'""');return e.includes(",")?'"'.concat(e,'"'):e}return a instanceof Date?a.toLocaleDateString():String(a)}).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),i=document.createElement("a");if(void 0!==i.download){let e=URL.createObjectURL(r);i.setAttribute("href",e),i.setAttribute("download","".concat(t,"_").concat(new Date().toISOString().split("T")[0],".csv")),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i)}}function r(e){return e.map(e=>{var t;return{Name:e.name,Email:e.email,Mobile:e.mobile,"Referral Code":e.referralCode,"Referred By":e.referredBy||"Direct",Plan:e.plan,"Active Days":e.activeDays,"Total Videos":e.totalVideos,"Today Videos":e.todayVideos,"Video Duration (seconds)":e.videoDuration||300,"Wallet Balance":e.wallet||0,Status:e.status,"Joined Date":(null==(t=e.joinedDate)?void 0:t.toLocaleDateString())||""}})}function i(e){return e.map(e=>{var t;return{"User ID":e.userId,"User Name":e.userName||"","User Email":e.userEmail||"",Type:e.type,Amount:e.amount,Description:e.description,Status:e.status,Date:(null==(t=e.date)?void 0:t.toLocaleDateString())||""}})}function l(e){return e.map(e=>{var t,a,s,r,i;return{"User Name":e.userName,"User Email":e.userEmail,"Mobile Number":e.userMobile||"","User Plan":e.userPlan||"","Active Days":e.userActiveDays||0,"Wallet Balance":e.walletBalance||0,"Withdrawal Amount":e.amount,"Account Holder":(null==(t=e.bankDetails)?void 0:t.accountHolderName)||"","Bank Name":(null==(a=e.bankDetails)?void 0:a.bankName)||"","Account Number":(null==(s=e.bankDetails)?void 0:s.accountNumber)||"","IFSC Code":(null==(r=e.bankDetails)?void 0:r.ifscCode)||"",Status:e.status,"Request Date":(null==(i=e.requestDate)?void 0:i.toLocaleDateString())||"","Admin Notes":e.adminNotes||""}})}function n(e){return e.map(e=>{var t,a;return{Title:e.title,Message:e.message,Type:e.type,Target:e.target,Status:e.status,"Created Date":(null==(t=e.createdAt)?void 0:t.toLocaleDateString())||"","Sent Date":(null==(a=e.sentAt)?void 0:a.toLocaleDateString())||""}})}a.d(t,{Bf:()=>s,Fz:()=>r,Pe:()=>n,dB:()=>l,sL:()=>i})},4880:(e,t,a)=>{Promise.resolve().then(a.bind(a,9119))},6104:(e,t,a)=>{"use strict";a.d(t,{db:()=>c,j2:()=>o});var s=a(3915),r=a(3004),i=a(5317),l=a(858);let n=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),o=(0,r.xI)(n),c=(0,i.aU)(n);(0,l.c7)(n)},6681:(e,t,a)=>{"use strict";a.d(t,{Nu:()=>o,hD:()=>n,wC:()=>c});var s=a(2115),r=a(3004),i=a(6104),l=a(12);function n(){let[e,t]=(0,s.useState)(null),[a,n]=(0,s.useState)(!0);(0,s.useEffect)(()=>{try{let e=(0,r.hg)(i.j2,e=>{console.log("Auth state changed:",e?"User logged in":"No user"),t(e),n(!1)});return()=>e()}catch(e){console.error("Error in auth state listener:",e),n(!1)}},[]);let o=async()=>{try{await (0,l.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:a,signOut:o}}function o(){let{user:e,loading:t}=n();return(0,s.useEffect)(()=>{t||e||(window.location.href="/login")},[e,t]),{user:e,loading:t}}function c(){let{user:e,loading:t}=n(),[a,r]=(0,s.useState)(!1),[i,l]=(0,s.useState)(!0);return(0,s.useEffect)(()=>{if(!t&&!e){window.location.href="/admin/login";return}if(e){let t=["<EMAIL>","<EMAIL>"].includes(e.email||"");r(t),l(!1),t||(window.location.href="/login")}},[e,t]),{user:e,loading:t||i,isAdmin:a}}},6779:(e,t,a)=>{"use strict";a.d(t,{Ki:()=>c,Pn:()=>n,TK:()=>u,getWithdrawals:()=>d,hG:()=>m,lo:()=>o,updateWithdrawalStatus:()=>h});var s=a(5317),r=a(6104),i=a(3592);let l=new Map;async function n(){let e="dashboard-stats",t=function(e){let t=l.get(e);return t&&Date.now()-t.timestamp<3e5?t.data:null}(e);if(t)return t;try{let t=new Date;t.setHours(0,0,0,0);let a=s.Dc.fromDate(t),n=await (0,s.GG)((0,s.rJ)(r.db,i.COLLECTIONS.users)),o=n.size,c=(0,s.P)((0,s.rJ)(r.db,i.COLLECTIONS.users),(0,s._M)(i.FIELD_NAMES.joinedDate,">=",a)),d=(await (0,s.GG)(c)).size,u=0,m=0,h=0,f=0;n.forEach(e=>{var a;let s=e.data();u+=s[i.FIELD_NAMES.totalVideos]||0,m+=s[i.FIELD_NAMES.wallet]||0;let r=null==(a=s[i.FIELD_NAMES.lastVideoDate])?void 0:a.toDate();r&&r.toDateString()===t.toDateString()&&(h+=s[i.FIELD_NAMES.todayVideos]||0)});try{let e=(0,s.P)((0,s.rJ)(r.db,i.COLLECTIONS.transactions),(0,s._M)(i.FIELD_NAMES.type,"==","video_earning"),(0,s.AB)(1e3));(await (0,s.GG)(e)).forEach(e=>{var a;let s=e.data(),r=null==(a=s[i.FIELD_NAMES.date])?void 0:a.toDate();r&&r>=t&&(f+=s[i.FIELD_NAMES.amount]||0)})}catch(e){console.warn("Could not fetch today's transactions:",e)}let g=(0,s.P)((0,s.rJ)(r.db,i.COLLECTIONS.withdrawals),(0,s._M)("status","==","pending")),x=(await (0,s.GG)(g)).size,y=(0,s.P)((0,s.rJ)(r.db,i.COLLECTIONS.withdrawals),(0,s._M)("date",">=",a)),p=(await (0,s.GG)(y)).size,b={totalUsers:o,totalVideos:u,totalEarnings:m,pendingWithdrawals:x,todayUsers:d,todayVideos:h,todayEarnings:f,todayWithdrawals:p};return l.set(e,{data:b,timestamp:Date.now()}),b}catch(e){throw console.error("Error getting admin dashboard stats:",e),e}}async function o(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,s.P)((0,s.rJ)(r.db,i.COLLECTIONS.users),(0,s.My)(i.FIELD_NAMES.joinedDate,"desc"),(0,s.AB)(e));t&&(a=(0,s.P)((0,s.rJ)(r.db,i.COLLECTIONS.users),(0,s.My)(i.FIELD_NAMES.joinedDate,"desc"),(0,s.HM)(t),(0,s.AB)(e)));let l=await (0,s.GG)(a);return{users:l.docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[i.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[i.FIELD_NAMES.planExpiry])?void 0:a.toDate()}}),lastDoc:l.docs[l.docs.length-1]||null,hasMore:l.docs.length===e}}catch(e){throw console.error("Error getting users:",e),e}}async function c(e){try{let t=(0,s.P)((0,s.rJ)(r.db,i.COLLECTIONS.users),(0,s._M)(i.FIELD_NAMES.mobile,"==",e));return(await (0,s.GG)(t)).docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[i.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[i.FIELD_NAMES.planExpiry])?void 0:a.toDate()}})}catch(e){throw console.error("Error searching users by mobile:",e),e}}async function d(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,s.P)((0,s.rJ)(r.db,i.COLLECTIONS.withdrawals),(0,s.My)("date","desc"),(0,s.AB)(e));t&&(a=(0,s.P)((0,s.rJ)(r.db,i.COLLECTIONS.withdrawals),(0,s.My)("date","desc"),(0,s.HM)(t),(0,s.AB)(e)));let l=await (0,s.GG)(a);return{withdrawals:l.docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data().date)?void 0:t.toDate()}}),lastDoc:l.docs[l.docs.length-1]||null,hasMore:l.docs.length===e}}catch(e){throw console.error("Error getting withdrawals:",e),e}}async function u(e,t){try{await (0,s.mZ)((0,s.H9)(r.db,i.COLLECTIONS.users,e),t),l.delete("dashboard-stats")}catch(e){throw console.error("Error updating user:",e),e}}async function m(e){try{await (0,s.kd)((0,s.H9)(r.db,i.COLLECTIONS.users,e)),l.delete("dashboard-stats")}catch(e){throw console.error("Error deleting user:",e),e}}async function h(e,t,n){try{let o=await (0,s.x7)((0,s.H9)(r.db,i.COLLECTIONS.withdrawals,e));if(!o.exists())throw Error("Withdrawal not found");let{userId:c,amount:d,status:u}=o.data(),m={status:t,updatedAt:s.Dc.now()};if(n&&(m.adminNotes=n),await (0,s.mZ)((0,s.H9)(r.db,i.COLLECTIONS.withdrawals,e),m),"approved"===t&&"approved"!==u){let{addTransaction:e}=await Promise.resolve().then(a.bind(a,3592));await e(c,{type:"withdrawal_approved",amount:0,description:"Withdrawal approved - ₹".concat(d," processed for transfer")})}if("rejected"===t&&"rejected"!==u){let{updateWalletBalance:e,addTransaction:t}=await Promise.resolve().then(a.bind(a,3592));await e(c,d),await t(c,{type:"withdrawal_rejected",amount:d,description:"Withdrawal rejected - ₹".concat(d," credited back to wallet")})}l.delete("dashboard-stats")}catch(e){throw console.error("Error updating withdrawal status:",e),e}}},9119:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>h});var s=a(5155),r=a(2115),i=a(6874),l=a.n(i),n=a(6681),o=a(3592),c=a(6779),d=a(3737),u=a(4752),m=a.n(u);function h(){let{user:e,loading:t,isAdmin:a}=(0,n.wC)(),[i,u]=(0,r.useState)([]),[h,f]=(0,r.useState)([]),[g,x]=(0,r.useState)(!0),[y,p]=(0,r.useState)(!1),[b,N]=(0,r.useState)(!1),[w,v]=(0,r.useState)([]),[j,E]=(0,r.useState)(!1),[D,C]=(0,r.useState)({title:"",message:"",type:"info",targetUsers:"all",selectedUserIds:[]});(0,r.useEffect)(()=>{a&&S()},[a]);let S=async()=>{try{x(!0);let[e,t]=await Promise.all([(0,o._f)(50),(0,c.lo)()]);u(e),f(t.users)}catch(e){console.error("Error loading data:",e),m().fire({icon:"error",title:"Error",text:"Failed to load data. Please try again."})}finally{x(!1)}},I=async()=>{try{N(!0),await (0,o.z8)({title:"Test Notification",message:"This is a test notification to verify the system is working correctly.",type:"info",targetUsers:"all",userIds:[],createdBy:(null==e?void 0:e.email)||"admin"}),m().fire({icon:"success",title:"Test Notification Sent!",text:"Test notification sent to all users. Check user dashboards to verify delivery.",timer:3e3,showConfirmButton:!1}),S()}catch(e){console.error("Error sending test notification:",e),m().fire({icon:"error",title:"Test Failed",text:"Failed to send test notification. Please try again."})}finally{N(!1)}},L=async()=>{try{if(!D.title.trim()||!D.message.trim())return void m().fire({icon:"error",title:"Validation Error",text:"Please fill in both title and message."});if("specific"===D.targetUsers&&0===D.selectedUserIds.length)return void m().fire({icon:"error",title:"Validation Error",text:"Please select at least one user for specific targeting."});N(!0),console.log("Sending notification:",{title:D.title.trim(),message:D.message.trim(),type:D.type,targetUsers:D.targetUsers,userIds:"specific"===D.targetUsers?D.selectedUserIds:[],createdBy:(null==e?void 0:e.email)||"admin"}),await (0,o.z8)({title:D.title.trim(),message:D.message.trim(),type:D.type,targetUsers:D.targetUsers,userIds:"specific"===D.targetUsers?D.selectedUserIds:[],createdBy:(null==e?void 0:e.email)||"admin"}),m().fire({icon:"success",title:"Notification Sent!",text:"Notification sent to ".concat("all"===D.targetUsers?"all users":"".concat(D.selectedUserIds.length," selected users"),"."),timer:3e3,showConfirmButton:!1}),C({title:"",message:"",type:"info",targetUsers:"all",selectedUserIds:[]}),p(!1),S()}catch(e){console.error("Error sending notification:",e),m().fire({icon:"error",title:"Send Failed",text:"Failed to send notification. Please try again."})}finally{N(!1)}},k=e=>{switch(e){case"success":return"fas fa-check-circle text-green-500";case"warning":return"fas fa-exclamation-triangle text-yellow-500";case"error":return"fas fa-times-circle text-red-500";default:return"fas fa-info-circle text-blue-500"}},U=e=>{let t=Math.floor((new Date().getTime()-e.getTime())/1e3);if(t<60)return"Just now";if(t<3600){let e=Math.floor(t/60);return"".concat(e," minute").concat(e>1?"s":""," ago")}if(t<86400){let e=Math.floor(t/3600);return"".concat(e," hour").concat(e>1?"s":""," ago")}{let e=Math.floor(t/86400);return"".concat(e," day").concat(e>1?"s":""," ago")}},A=async(e,t)=>{if((await m().fire({icon:"warning",title:"Delete Notification",text:'Are you sure you want to delete "'.concat(t,'"? This action cannot be undone.'),showCancelButton:!0,confirmButtonText:"Yes, Delete",confirmButtonColor:"#dc2626",cancelButtonText:"Cancel"})).isConfirmed)try{E(!0),await (0,o.fP)(e),u(t=>t.filter(t=>t.id!==e)),m().fire({icon:"success",title:"Notification Deleted",text:"Notification has been deleted successfully",timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error deleting notification:",e),m().fire({icon:"error",title:"Delete Failed",text:"Failed to delete notification. Please try again."})}finally{E(!1)}},T=async()=>{if(0===w.length)return void m().fire({icon:"warning",title:"No Selection",text:"Please select notifications to delete."});if((await m().fire({icon:"warning",title:"Delete Selected Notifications",text:"Are you sure you want to delete ".concat(w.length," selected notifications? This action cannot be undone."),showCancelButton:!0,confirmButtonText:"Yes, Delete All",confirmButtonColor:"#dc2626",cancelButtonText:"Cancel"})).isConfirmed)try{E(!0),await Promise.all(w.map(e=>(0,o.fP)(e))),u(e=>e.filter(e=>!w.includes(e.id))),v([]),m().fire({icon:"success",title:"Notifications Deleted",text:"".concat(w.length," notifications have been deleted successfully"),timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error deleting notifications:",e),m().fire({icon:"error",title:"Delete Failed",text:"Failed to delete some notifications. Please try again."})}finally{E(!1)}},B=e=>{v(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])};return t||g?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading notifications..."})]})}):(0,s.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,s.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(l(),{href:"/admin",className:"text-gray-500 hover:text-gray-700",children:(0,s.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Notifications"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("span",{className:"text-gray-700",children:["Total: ",i.length,w.length>0&&(0,s.jsxs)("span",{className:"ml-2 text-blue-600",children:["(",w.length," selected)"]})]}),w.length>0&&(0,s.jsxs)("button",{onClick:T,disabled:j,className:"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,s.jsx)("i",{className:"fas fa-trash mr-2"}),"Delete Selected (",w.length,")"]}),(0,s.jsxs)("button",{onClick:I,disabled:b||j,className:"bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,s.jsx)("i",{className:"fas fa-vial mr-2"}),"Test Notification"]}),(0,s.jsxs)("button",{onClick:()=>{if(0===i.length)return void m().fire({icon:"warning",title:"No Data",text:"No notifications to export."});let e=(0,d.Pe)(i);(0,d.Bf)(e,"notifications"),m().fire({icon:"success",title:"Export Complete",text:"Exported ".concat(i.length," notifications to CSV file."),timer:2e3,showConfirmButton:!1})},disabled:j,className:"bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,s.jsx)("i",{className:"fas fa-download mr-2"}),"Export CSV"]}),(0,s.jsxs)("button",{onClick:()=>p(!0),disabled:j,className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,s.jsx)("i",{className:"fas fa-plus mr-2"}),"Send Notification"]}),(0,s.jsxs)("button",{onClick:S,disabled:j,className:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,s.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),(0,s.jsx)("div",{className:"p-6",children:(0,s.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:0===i.length?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("i",{className:"fas fa-bell-slash text-gray-300 text-6xl mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No notifications sent yet"}),(0,s.jsx)("p",{className:"text-gray-500 mb-4",children:"Start by sending your first notification to users"}),(0,s.jsxs)("button",{onClick:()=>p(!0),className:"bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-plus mr-2"}),"Send First Notification"]})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"bg-gray-50 px-6 py-3 border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",checked:w.length===i.length&&i.length>0,onChange:()=>{w.length===i.length?v([]):v(i.map(e=>e.id).filter(Boolean))},className:"mr-3"}),(0,s.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:["Select All (",i.length," notifications)"]})]}),w.length>0&&(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)("span",{className:"text-sm text-gray-600",children:[w.length," selected"]}),(0,s.jsxs)("button",{onClick:T,disabled:j,className:"text-red-600 hover:text-red-800 disabled:opacity-50",children:[(0,s.jsx)("i",{className:"fas fa-trash mr-1"}),"Delete Selected"]})]})]})}),(0,s.jsx)("div",{className:"divide-y divide-gray-200",children:i.map(e=>{var t;return(0,s.jsx)("div",{className:"p-6 hover:bg-gray-50 ".concat(w.includes(e.id)?"bg-blue-50":""),children:(0,s.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,s.jsx)("div",{className:"flex-shrink-0 mt-1",children:(0,s.jsx)("input",{type:"checkbox",checked:w.includes(e.id),onChange:()=>B(e.id),className:"mr-3"})}),(0,s.jsx)("div",{className:"flex-shrink-0 mt-1",children:(0,s.jsx)("i",{className:k(e.type)})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("h3",{className:"text-lg font-medium text-gray-900 flex items-center",children:[e.title,(0,s.jsx)("span",{className:"ml-2 px-2 py-1 text-xs font-bold bg-red-100 text-red-800 rounded-full",children:"\uD83D\uDEA8 BLOCKING"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat("success"===e.type?"bg-green-100 text-green-800":"warning"===e.type?"bg-yellow-100 text-yellow-800":"error"===e.type?"bg-red-100 text-red-800":"bg-blue-100 text-blue-800"),children:e.type.charAt(0).toUpperCase()+e.type.slice(1)}),(0,s.jsx)("button",{onClick:()=>A(e.id,e.title),disabled:j,className:"text-red-600 hover:text-red-800 disabled:opacity-50 p-1",title:"Delete notification",children:(0,s.jsx)("i",{className:"fas fa-trash"})})]})]}),(0,s.jsx)("p",{className:"text-gray-700 mt-2",children:e.message}),(0,s.jsx)("div",{className:"flex items-center justify-between mt-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,s.jsxs)("span",{children:[(0,s.jsx)("i",{className:"fas fa-user mr-1"}),"By: ",e.createdBy]}),(0,s.jsxs)("span",{children:[(0,s.jsx)("i",{className:"fas fa-users mr-1"}),"Target: ","all"===e.targetUsers?"All Users":"".concat((null==(t=e.userIds)?void 0:t.length)||0," Selected Users")]}),(0,s.jsxs)("span",{children:[(0,s.jsx)("i",{className:"fas fa-clock mr-1"}),U(e.createdAt)]})]})})]})]})},e.id)})})]})})}),y&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:"Send Notification"}),(0,s.jsx)("button",{onClick:()=>p(!1),className:"text-gray-500 hover:text-gray-700",children:(0,s.jsx)("i",{className:"fas fa-times text-xl"})})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Title"}),(0,s.jsx)("input",{type:"text",value:D.title,onChange:e=>C(t=>({...t,title:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",placeholder:"Enter notification title..."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Message"}),(0,s.jsx)("textarea",{value:D.message,onChange:e=>C(t=>({...t,message:e.target.value})),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",placeholder:"Enter notification message..."})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Type"}),(0,s.jsxs)("select",{value:D.type,onChange:e=>C(t=>({...t,type:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"info",children:"Info"}),(0,s.jsx)("option",{value:"success",children:"Success"}),(0,s.jsx)("option",{value:"warning",children:"Warning"}),(0,s.jsx)("option",{value:"error",children:"Error"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Target"}),(0,s.jsxs)("select",{value:D.targetUsers,onChange:e=>C(t=>({...t,targetUsers:e.target.value,selectedUserIds:"all"===e.target.value?[]:t.selectedUserIds})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"all",children:"All Users"}),(0,s.jsx)("option",{value:"specific",children:"Specific Users"})]})]})]}),(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)("i",{className:"fas fa-exclamation-triangle text-red-500 mt-1"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:"\uD83D\uDEA8 All Notifications are Blocking (Mandatory)"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Users must acknowledge this notification before they can continue with any activities (watching videos, accessing dashboard features, etc.)"})]})]})}),"specific"===D.targetUsers&&(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Users"}),(0,s.jsx)("div",{className:"max-h-40 overflow-y-auto border border-gray-300 rounded-lg p-2",children:h.map(e=>(0,s.jsxs)("label",{className:"flex items-center p-2 hover:bg-gray-50 rounded",children:[(0,s.jsx)("input",{type:"checkbox",checked:D.selectedUserIds.includes(e.id),onChange:t=>{t.target.checked?C(t=>({...t,selectedUserIds:[...t.selectedUserIds,e.id]})):C(t=>({...t,selectedUserIds:t.selectedUserIds.filter(t=>t!==e.id)}))},className:"mr-3"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium text-gray-900",children:e.name||"Unknown User"}),(0,s.jsxs)("div",{className:"text-sm text-gray-500",children:[e.email||"No email"," • ",e.plan||"No plan"]})]})]},e.id))}),(0,s.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:[D.selectedUserIds.length," user(s) selected"]})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-4 mt-6",children:[(0,s.jsx)("button",{onClick:()=>p(!1),className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300",children:"Cancel"}),(0,s.jsx)("button",{onClick:L,disabled:b,className:"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50",children:b?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Sending..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-paper-plane mr-2"}),"Send Notification"]})})]})]})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,3592,8441,1684,7358],()=>t(4880)),_N_E=e.O()}]);