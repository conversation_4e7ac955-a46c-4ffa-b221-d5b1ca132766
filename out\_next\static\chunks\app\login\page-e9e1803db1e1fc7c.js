(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4520],{12:(e,t,r)=>{"use strict";r.d(t,{M4:()=>l,_f:()=>n});var a=r(6104),s=r(4752),i=r.n(s);function o(e){try{Object.keys(localStorage).forEach(t=>{(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function n(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await i().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&o(e),await a.j2.signOut(),i().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),i().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{e&&o(e),await a.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}},1469:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return n}});let a=r(8229),s=r(8883),i=r(3063),o=a._(r(1193));function n(e){let{props:t}=(0,s.getImgProps)(e,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=i.Image},2590:(e,t,r)=>{Promise.resolve().then(r.bind(r,9690))},6104:(e,t,r)=>{"use strict";r.d(t,{db:()=>c,j2:()=>l});var a=r(3915),s=r(3004),i=r(5317),o=r(858);let n=(0,a.Dk)().length?(0,a.Sx)():(0,a.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),l=(0,s.xI)(n),c=(0,i.aU)(n);(0,o.c7)(n)},6681:(e,t,r)=>{"use strict";r.d(t,{Nu:()=>l,hD:()=>n,wC:()=>c});var a=r(2115),s=r(3004),i=r(6104),o=r(12);function n(){let[e,t]=(0,a.useState)(null),[r,n]=(0,a.useState)(!0);(0,a.useEffect)(()=>{try{let e=(0,s.hg)(i.j2,e=>{console.log("Auth state changed:",e?"User logged in":"No user"),t(e),n(!1)});return()=>e()}catch(e){console.error("Error in auth state listener:",e),n(!1)}},[]);let l=async()=>{try{await (0,o.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:r,signOut:l}}function l(){let{user:e,loading:t}=n();return(0,a.useEffect)(()=>{t||e||(window.location.href="/login")},[e,t]),{user:e,loading:t}}function c(){let{user:e,loading:t}=n(),[r,s]=(0,a.useState)(!1),[i,o]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{if(!t&&!e){window.location.href="/admin/login";return}if(e){let t=["<EMAIL>","<EMAIL>"].includes(e.email||"");s(t),o(!1),t||(window.location.href="/login")}},[e,t]),{user:e,loading:t||i,isAdmin:r}}},6766:(e,t,r)=>{"use strict";r.d(t,{default:()=>s.a});var a=r(1469),s=r.n(a)},9690:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var a=r(5155),s=r(2115),i=r(6874),o=r.n(i),n=r(6766),l=r(3004),c=r(6104),d=r(6681),u=r(4752),m=r.n(u);function h(){let{user:e,loading:t}=(0,d.hD)(),[r,i]=(0,s.useState)(""),[u,h]=(0,s.useState)(""),[f,g]=(0,s.useState)(!1),[x,w]=(0,s.useState)(!1);(0,s.useEffect)(()=>{e&&!t&&(window.location.href="/dashboard")},[e,t]);let b=async e=>{if(e.preventDefault(),!r||!u)return void m().fire({icon:"error",title:"Error",text:"Please fill in all fields",background:"rgba(255, 255, 255, 0.95)",backdrop:"rgba(0, 0, 0, 0.8)"});g(!0);try{await (0,l.x9)(c.j2,r,u)}catch(t){console.error("Login error:",t);let e="An error occurred during login";switch(t.code){case"auth/user-not-found":e="No account found with this email address";break;case"auth/wrong-password":e="Incorrect password";break;case"auth/invalid-email":e="Invalid email address";break;case"auth/user-disabled":e="This account has been disabled";break;case"auth/too-many-requests":e="Too many failed attempts. Please try again later";break;default:e=t.message||"Login failed"}m().fire({icon:"error",title:"Login Failed",text:e,background:"rgba(255, 255, 255, 0.95)",backdrop:"rgba(0, 0, 0, 0.8)"}),h("")}finally{g(!1)}};return t?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)("div",{className:"spinner"})}):(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4",children:(0,a.jsxs)("div",{className:"glass-card w-full max-w-md p-8",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,a.jsx)(n.default,{src:"/img/mytube-logo.svg",alt:"MyTube Logo",width:50,height:50,className:"mr-3"}),(0,a.jsx)("span",{className:"text-2xl font-bold text-white",children:"MyTube"})]}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-white mb-2",children:"Welcome Back"}),(0,a.jsx)("p",{className:"text-white/80",children:"Sign in to continue earning"})]}),(0,a.jsxs)("form",{onSubmit:b,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-white font-medium mb-2",children:"Email Address"}),(0,a.jsx)("input",{type:"email",id:"email",value:r,onChange:e=>i(e.target.value),className:"form-input",placeholder:"Enter your email",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-white font-medium mb-2",children:"Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:x?"text":"password",id:"password",value:u,onChange:e=>h(e.target.value),className:"form-input pr-12",placeholder:"Enter your password",required:!0}),(0,a.jsx)("button",{type:"button",onClick:()=>w(!x),className:"password-toggle-btn","aria-label":x?"Hide password":"Show password",children:(0,a.jsx)("i",{className:"fas ".concat(x?"fa-eye-slash":"fa-eye")})})]})]}),(0,a.jsx)("button",{type:"submit",disabled:f,className:"w-full btn-primary flex items-center justify-center",children:f?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Logging in..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-sign-in-alt mr-2"}),"Login"]})})]}),(0,a.jsxs)("div",{className:"mt-6 text-center space-y-3",children:[(0,a.jsx)(o(),{href:"/forgot-password",className:"text-white/80 hover:text-white transition-colors",children:"Forgot your password?"}),(0,a.jsxs)("div",{className:"text-white/60",children:["Don't have an account?"," ",(0,a.jsx)(o(),{href:"/register",className:"text-white font-semibold hover:underline",children:"Sign up here"})]})]}),(0,a.jsx)("div",{className:"mt-8 text-center",children:(0,a.jsxs)(o(),{href:"/",className:"text-white/80 hover:text-white transition-colors inline-flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Home"]})})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,3063,8441,1684,7358],()=>t(2590)),_N_E=e.O()}]);