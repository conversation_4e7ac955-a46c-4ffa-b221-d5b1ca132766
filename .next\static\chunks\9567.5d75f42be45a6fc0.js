"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9567],{9567:(e,a,t)=>{t.d(a,{applyUserLeave:()=>u,cancelUserLeave:()=>g,createAdminLeave:()=>c,debugAdminLeaveStatus:()=>d,deleteAdminLeave:()=>l,getAdminLeaves:()=>s,getUserLeaves:()=>v,getUserMonthlyLeaveCount:()=>m,isAdminLeaveDay:()=>i,isUserOnLeave:()=>y,q8:()=>D});var o=t(6104),r=t(5317);let n={adminLeaves:"adminLeaves",userLeaves:"userLeaves"};async function c(e){try{return(await (0,r.gS)((0,r.rJ)(o.db,n.adminLeaves),{...e,date:r.Dc.fromDate(e.date),createdAt:r.Dc.now()})).id}catch(e){throw console.error("Error creating admin leave:",e),e}}async function s(){try{let e=(0,r.P)((0,r.rJ)(o.db,n.adminLeaves),(0,r.My)("date","asc")),a=(await (0,r.GG)(e)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate(),createdAt:e.data().createdAt.toDate()}));return console.log("\uD83D\uDCC5 All admin leaves:",a),a}catch(e){throw console.error("Error getting admin leaves:",e),e}}async function d(){try{let e=new Date;console.log("\uD83D\uDD0D Debug: Checking admin leave status for today:",e.toDateString());let a=await i(e);console.log("\uD83D\uDCCA Debug: Admin leave result:",a);let t=await s();console.log("\uD83D\uDCC5 Debug: All admin leaves in database:",t);let o=t.filter(a=>a.date.toDateString()===e.toDateString());console.log("\uD83D\uDCC5 Debug: Today's admin leaves:",o)}catch(e){console.error("❌ Debug: Error checking admin leave status:",e)}}async function l(e){try{await (0,r.kd)((0,r.H9)(o.db,n.adminLeaves,e))}catch(e){throw console.error("Error deleting admin leave:",e),e}}async function i(e){try{let a=new Date(e);a.setHours(0,0,0,0);let t=new Date(e);t.setHours(23,59,59,999),console.log("\uD83D\uDD0D Checking admin leave for date range:",a.toISOString(),"to",t.toISOString());let c=(0,r.P)((0,r.rJ)(o.db,n.adminLeaves),(0,r._M)("date",">=",r.Dc.fromDate(a)),(0,r._M)("date","<=",r.Dc.fromDate(t))),s=await (0,r.GG)(c),d=!s.empty;return d?console.log("\uD83D\uDCC5 Found admin leave(s) for today:",s.docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate()}))):console.log("\uD83D\uDCC5 No admin leaves found for today"),d}catch(e){return console.error("❌ Error checking admin leave day:",e),!1}}async function u(e){try{let a,t,c,s=new Date,d=s.getFullYear(),l=s.getMonth()+1,i=await m(e.userId,d,l),u="pending";return i<4&&(u="approved",a="system",c=r.Dc.now(),t="Auto-approved: ".concat(i+1,"/").concat(4," monthly leaves used")),{id:(await (0,r.gS)((0,r.rJ)(o.db,n.userLeaves),{...e,date:r.Dc.fromDate(e.date),status:u,appliedAt:r.Dc.now(),...a&&{reviewedBy:a},...c&&{reviewedAt:c},...t&&{reviewNotes:t}})).id,autoApproved:"approved"===u,usedLeaves:i+ +("approved"===u),maxLeaves:4}}catch(e){throw console.error("Error applying user leave:",e),e}}async function v(e){try{let a=(0,r.P)((0,r.rJ)(o.db,n.userLeaves),(0,r._M)("userId","==",e),(0,r.My)("date","desc"));return(await (0,r.GG)(a)).docs.map(e=>{var a;return{id:e.id,...e.data(),date:e.data().date.toDate(),appliedAt:e.data().appliedAt.toDate(),reviewedAt:null==(a=e.data().reviewedAt)?void 0:a.toDate()}})}catch(e){throw console.error("Error getting user leaves:",e),e}}async function g(e){try{await (0,r.kd)((0,r.H9)(o.db,n.userLeaves,e))}catch(e){throw console.error("Error cancelling user leave:",e),e}}async function m(e,a,t){try{let c=new Date(a,t-1,1),s=new Date(a,t,0,23,59,59,999),d=(0,r.P)((0,r.rJ)(o.db,n.userLeaves),(0,r._M)("userId","==",e),(0,r._M)("status","==","approved"),(0,r._M)("date",">=",r.Dc.fromDate(c)),(0,r._M)("date","<=",r.Dc.fromDate(s)));return(await (0,r.GG)(d)).size}catch(e){return console.error("Error getting user monthly leave count:",e),0}}async function y(e,a){try{let t=new Date(a);t.setHours(0,0,0,0);let c=new Date(a);c.setHours(23,59,59,999),console.log("\uD83D\uDD0D Checking user leave for user:",e,"on date range:",t.toISOString(),"to",c.toISOString());let s=(0,r.P)((0,r.rJ)(o.db,n.userLeaves),(0,r._M)("userId","==",e),(0,r._M)("status","==","approved"),(0,r._M)("date",">=",r.Dc.fromDate(t)),(0,r._M)("date","<=",r.Dc.fromDate(c))),d=await (0,r.GG)(s),l=!d.empty;return l?console.log("\uD83D\uDC64 Found user leave(s) for today:",d.docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate()}))):console.log("\uD83D\uDC64 No user leaves found for today"),l}catch(e){return console.error("❌ Error checking user leave day:",e),!1}}async function D(e){try{let a=new Date;console.log("\uD83D\uDD0D Checking work block status for user:",e,"on date:",a.toDateString());try{let e=await i(a);if(console.log("\uD83D\uDCC5 Admin leave check result:",e),e)return console.log("\uD83D\uDEAB Work blocked due to admin leave"),{blocked:!0,reason:"System maintenance/holiday"}}catch(e){console.error("❌ Error checking admin leave (allowing work to continue):",e)}try{let t=await y(e,a);if(console.log("\uD83D\uDC64 User leave check result:",t),t)return console.log("\uD83D\uDEAB Work blocked due to user leave"),{blocked:!0,reason:"You are on approved leave today"}}catch(e){console.error("❌ Error checking user leave (allowing work to continue):",e)}return console.log("✅ Work is not blocked"),{blocked:!1}}catch(e){return console.error("❌ Error checking work block status (allowing work to continue):",e),{blocked:!1}}}}}]);