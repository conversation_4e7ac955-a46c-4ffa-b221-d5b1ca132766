(()=>{var e={};e.id=9451,e.ids=[9451],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},1291:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["admin",{children:["leaves",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,23907)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\leaves\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\leaves\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/leaves/page",pathname:"/admin/leaves",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16358:(e,t,r)=>{Promise.resolve().then(r.bind(r,46661))},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},23907:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\leaves\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\leaves\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,t,r)=>{"use strict";r.d(t,{db:()=>c,j2:()=>l});var s=r(67989),a=r(63385),i=r(75535),n=r(70146);let o=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),l=(0,a.xI)(o),c=(0,i.aU)(o);(0,n.c7)(o)},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},46661:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(60687),a=r(43210),i=r(85814),n=r.n(i),o=r(87979),l=r(83475),c=r(77567);function d(){let{user:e,loading:t,isAdmin:i}=(0,o.wC)(),[d,u]=(0,a.useState)([]),[x,p]=(0,a.useState)(!0),[m,h]=(0,a.useState)(!1),[g,f]=(0,a.useState)(!1),[y,b]=(0,a.useState)({date:"",reason:"",type:"holiday"}),v=async()=>{try{p(!0);let{getAdminLeaves:e}=await r.e(7087).then(r.bind(r,87087)),t=await e();u(t)}catch(e){console.error("Error loading leaves:",e),u([]),c.A.fire({icon:"error",title:"Error",text:"Failed to load leaves. Please try again."})}finally{p(!1)}},w=async()=>{try{if(!y.date||!y.reason.trim())return void c.A.fire({icon:"error",title:"Validation Error",text:"Please fill in all required fields."});let t=new Date(y.date),s=new Date;if(s.setHours(0,0,0,0),t<s)return void c.A.fire({icon:"error",title:"Invalid Date",text:"Cannot create leave for past dates."});if(d.find(e=>e.date.toDateString()===t.toDateString()))return void c.A.fire({icon:"error",title:"Duplicate Leave",text:"Leave already exists for this date."});f(!0);let{createAdminLeave:a}=await r.e(7087).then(r.bind(r,87087));await a({date:t,reason:y.reason.trim(),type:y.type,createdBy:e?.email||"admin"}),await v(),c.A.fire({icon:"success",title:"Leave Created!",text:`Admin leave created for ${t.toLocaleDateString()}.`,timer:3e3,showConfirmButton:!1}),b({date:"",reason:"",type:"holiday"}),h(!1)}catch(e){console.error("Error creating leave:",e),c.A.fire({icon:"error",title:"Creation Failed",text:"Failed to create leave. Please try again."})}finally{f(!1)}},j=async e=>{try{if((await c.A.fire({title:"Delete Leave",text:"Are you sure you want to delete this leave?",icon:"warning",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Delete",cancelButtonText:"Cancel"})).isConfirmed){let{deleteAdminLeave:t}=await r.e(7087).then(r.bind(r,87087));await t(e),await v(),c.A.fire({icon:"success",title:"Leave Deleted",text:"Leave has been deleted successfully.",timer:2e3,showConfirmButton:!1})}}catch(e){console.error("Error deleting leave:",e),c.A.fire({icon:"error",title:"Delete Failed",text:"Failed to delete leave. Please try again."})}},N=e=>{switch(e){case"holiday":return"bg-green-100 text-green-800";case"maintenance":return"bg-blue-100 text-blue-800";case"emergency":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},C=e=>{switch(e){case"holiday":return"fas fa-calendar-day text-green-500";case"maintenance":return"fas fa-tools text-blue-500";case"emergency":return"fas fa-exclamation-triangle text-red-500";default:return"fas fa-calendar text-gray-500"}};return t||x?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading leaves..."})]})}):(0,s.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,s.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(n(),{href:"/admin",className:"text-gray-500 hover:text-gray-700",children:(0,s.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Leave Management"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("span",{className:"text-gray-700",children:["Total: ",d.length]}),(0,s.jsxs)("button",{onClick:()=>{if(0===d.length)return void c.A.fire({icon:"warning",title:"No Data",text:"No leaves to export."});let e=d.map(e=>({Date:e.date.toLocaleDateString(),Reason:e.reason,Type:e.type.charAt(0).toUpperCase()+e.type.slice(1),"Created By":e.createdBy,"Created At":e.createdAt.toLocaleDateString()}));(0,l.Bf)(e,"admin-leaves"),c.A.fire({icon:"success",title:"Export Complete",text:`Exported ${d.length} leaves to CSV file.`,timer:2e3,showConfirmButton:!1})},className:"bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-download mr-2"}),"Export CSV"]}),(0,s.jsxs)("button",{onClick:()=>h(!0),className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-plus mr-2"}),"Add Leave"]}),(0,s.jsxs)("button",{onClick:v,className:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),(0,s.jsx)("div",{className:"p-6",children:(0,s.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:0===d.length?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("i",{className:"fas fa-calendar-times text-gray-300 text-6xl mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No leaves scheduled"}),(0,s.jsx)("p",{className:"text-gray-500 mb-4",children:"Create your first admin leave to block work and withdrawals"}),(0,s.jsxs)("button",{onClick:()=>h(!0),className:"bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-plus mr-2"}),"Add First Leave"]})]}):(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Reason"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created By"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:d.map(e=>(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.date.toLocaleDateString()}),(0,s.jsx)("div",{className:"text-xs text-gray-500",children:e.date.toLocaleDateString("en-US",{weekday:"long"})})]}),(0,s.jsx)("td",{className:"px-6 py-4",children:(0,s.jsx)("div",{className:"text-sm text-gray-900 max-w-xs",children:e.reason})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${N(e.type)}`,children:[(0,s.jsx)("i",{className:`${C(e.type)} mr-1`}),e.type.charAt(0).toUpperCase()+e.type.slice(1)]})}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,s.jsx)("div",{className:"text-sm text-gray-900",children:e.createdBy}),(0,s.jsx)("div",{className:"text-xs text-gray-500",children:e.createdAt.toLocaleDateString()})]}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,s.jsxs)("button",{onClick:()=>j(e.id),className:"text-red-600 hover:text-red-900",children:[(0,s.jsx)("i",{className:"fas fa-trash mr-1"}),"Delete"]})})]},e.id))})]})})})}),m&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:"Add Admin Leave"}),(0,s.jsx)("button",{onClick:()=>h(!1),className:"text-gray-500 hover:text-gray-700",children:(0,s.jsx)("i",{className:"fas fa-times text-xl"})})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Date"}),(0,s.jsx)("input",{type:"date",value:y.date,onChange:e=>b(t=>({...t,date:e.target.value})),min:new Date().toISOString().split("T")[0],className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Type"}),(0,s.jsxs)("select",{value:y.type,onChange:e=>b(t=>({...t,type:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"holiday",children:"Holiday"}),(0,s.jsx)("option",{value:"maintenance",children:"Maintenance"}),(0,s.jsx)("option",{value:"emergency",children:"Emergency"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Reason"}),(0,s.jsx)("textarea",{value:y.reason,onChange:e=>b(t=>({...t,reason:e.target.value})),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",placeholder:"Enter reason for leave..."})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-4 mt-6",children:[(0,s.jsx)("button",{onClick:()=>h(!1),className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300",children:"Cancel"}),(0,s.jsx)("button",{onClick:w,disabled:g,className:"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50",children:g?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Creating..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-plus mr-2"}),"Create Leave"]})})]})]})})]})}},47902:(e,t,r)=>{Promise.resolve().then(r.bind(r,23907))},51278:(e,t,r)=>{"use strict";r.d(t,{M4:()=>o,_f:()=>n});var s=r(33784),a=r(77567);function i(e){try{Object.keys(localStorage).forEach(t=>{(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function n(e,t="/login"){try{if((await a.A.fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&i(e),await s.j2.signOut(),a.A.fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),a.A.fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function o(e,t="/login"){try{e&&i(e),await s.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83475:(e,t,r)=>{"use strict";function s(e,t,r){if(!e||0===e.length)return void alert("No data to export");let s=r||Object.keys(e[0]),a=new Blob([[s.join(","),...e.map(e=>s.map(t=>{let r=e[t];if(null==r)return"";if("string"==typeof r){let e=r.replace(/"/g,'""');return e.includes(",")?`"${e}"`:e}return r instanceof Date?r.toLocaleDateString():String(r)}).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),i=document.createElement("a");if(void 0!==i.download){let e=URL.createObjectURL(a);i.setAttribute("href",e),i.setAttribute("download",`${t}_${new Date().toISOString().split("T")[0]}.csv`),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i)}}function a(e){return e.map(e=>({Name:e.name,Email:e.email,Mobile:e.mobile,"Referral Code":e.referralCode,"Referred By":e.referredBy||"Direct",Plan:e.plan,"Active Days":e.activeDays,"Total Videos":e.totalVideos,"Today Videos":e.todayVideos,"Video Duration (seconds)":e.videoDuration||300,"Wallet Balance":e.wallet||0,Status:e.status,"Joined Date":e.joinedDate?.toLocaleDateString()||""}))}function i(e){return e.map(e=>({"User ID":e.userId,"User Name":e.userName||"","User Email":e.userEmail||"",Type:e.type,Amount:e.amount,Description:e.description,Status:e.status,Date:e.date?.toLocaleDateString()||""}))}function n(e){return e.map(e=>({"User Name":e.userName,"User Email":e.userEmail,"Mobile Number":e.userMobile||"","User Plan":e.userPlan||"","Active Days":e.userActiveDays||0,"Wallet Balance":e.walletBalance||0,"Withdrawal Amount":e.amount,"Account Holder":e.bankDetails?.accountHolderName||"","Bank Name":e.bankDetails?.bankName||"","Account Number":e.bankDetails?.accountNumber||"","IFSC Code":e.bankDetails?.ifscCode||"",Status:e.status,"Request Date":e.requestDate?.toLocaleDateString()||"","Admin Notes":e.adminNotes||""}))}function o(e){return e.map(e=>({Title:e.title,Message:e.message,Type:e.type,Target:e.target,Status:e.status,"Created Date":e.createdAt?.toLocaleDateString()||"","Sent Date":e.sentAt?.toLocaleDateString()||""}))}r.d(t,{Bf:()=>s,Fz:()=>a,Pe:()=>o,dB:()=>n,sL:()=>i})},87979:(e,t,r)=>{"use strict";r.d(t,{Nu:()=>n,hD:()=>i,wC:()=>o});var s=r(43210);r(63385),r(33784);var a=r(51278);function i(){let[e,t]=(0,s.useState)(null),[r,i]=(0,s.useState)(!0),n=async()=>{try{await (0,a.M4)(e?.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:r,signOut:n}}function n(){let{user:e,loading:t}=i();return{user:e,loading:t}}function o(){let{user:e,loading:t}=i(),[r,a]=(0,s.useState)(!1),[n,o]=(0,s.useState)(!0);return{user:e,loading:t||n,isAdmin:r}}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[6204,2756,7567,8441],()=>r(1291));module.exports=s})();