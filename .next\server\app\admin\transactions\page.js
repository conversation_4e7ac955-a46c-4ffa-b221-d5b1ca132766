(()=>{var e={};e.id=4426,e.ids=[4426],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},8574:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\transactions\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\transactions\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},22245:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["admin",{children:["transactions",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8574)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\transactions\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\transactions\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/transactions/page",pathname:"/admin/transactions",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,t,r)=>{"use strict";r.d(t,{db:()=>c,j2:()=>l});var s=r(67989),a=r(63385),i=r(75535),n=r(70146);let o=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),l=(0,a.xI)(o),c=(0,i.aU)(o);(0,n.c7)(o)},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41772:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var s=r(60687),a=r(43210),i=r(85814),n=r.n(i),o=r(87979),l=r(75535),c=r(33784),d=r(3582),u=r(83475),p=r(77567);function x(){let{user:e,loading:t,isAdmin:r}=(0,o.wC)(),[i,x]=(0,a.useState)([]),[m,h]=(0,a.useState)(!0),[g,f]=(0,a.useState)(""),[y,b]=(0,a.useState)(""),[v,w]=(0,a.useState)(""),[N,j]=(0,a.useState)(1),S=async()=>{try{h(!0);let e=(0,l.P)((0,l.rJ)(c.db,d.COLLECTIONS.transactions),(0,l.My)("date","desc"),(0,l.AB)(500)),t=await (0,l.GG)(e),r=[];for(let e of t.docs){let t=e.data(),s="Unknown User",a="<EMAIL>",i="Unknown Mobile";try{let e=await (0,l.GG)((0,l.P)((0,l.rJ)(c.db,d.COLLECTIONS.users),(0,l._M)("__name__","==",t.userId)));if(!e.empty){let t=e.docs[0].data();s=t.name||"Unknown User",a=t.email||"<EMAIL>",i=t.mobile||"Unknown Mobile"}}catch(e){console.error("Error fetching user data:",e)}r.push({id:e.id,userId:t.userId,userName:s,userEmail:a,userMobile:i,type:t.type,amount:t.amount,description:t.description,date:t.date?.toDate()||new Date,status:t.status||"completed"})}x(r)}catch(e){console.error("Error loading transactions:",e),p.A.fire({icon:"error",title:"Error",text:"Failed to load transactions. Please try again."})}finally{h(!1)}},C=i.filter(e=>{let t=!g||e.type===g,r=!y||e.status===y,s=!v||e.userName.toLowerCase().includes(v.toLowerCase())||e.userEmail.toLowerCase().includes(v.toLowerCase())||e.userMobile.toLowerCase().includes(v.toLowerCase())||e.description.toLowerCase().includes(v.toLowerCase());return t&&r&&s}),D=Math.ceil(C.length/20),k=(N-1)*20,q=C.slice(k,k+20),A=e=>null==e||isNaN(e)?"₹0.00":new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0,maximumFractionDigits:2}).format(e),_=e=>{switch(e){case"video_earning":return"Video Earning";case"withdrawal":return"Withdrawal";case"bonus":return"Bonus";case"referral":return"Referral";default:return e.charAt(0).toUpperCase()+e.slice(1)}},E=e=>{switch(e){case"video_earning":return"fas fa-play-circle text-green-500";case"withdrawal":return"fas fa-download text-red-500";case"bonus":return"fas fa-gift text-yellow-500";case"referral":return"fas fa-users text-blue-500";default:return"fas fa-exchange-alt text-gray-500"}};return t||m?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading transactions..."})]})}):(0,s.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,s.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(n(),{href:"/admin",className:"text-gray-500 hover:text-gray-700",children:(0,s.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Transactions"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("span",{className:"text-gray-700",children:["Total: ",C.length]}),(0,s.jsxs)("button",{onClick:()=>{if(0===C.length)return void p.A.fire({icon:"warning",title:"No Data",text:"No transactions to export."});let e=(0,u.sL)(C);(0,u.Bf)(e,"transactions"),p.A.fire({icon:"success",title:"Export Complete",text:`Exported ${C.length} transactions to CSV file.`,timer:2e3,showConfirmButton:!1})},className:"bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-download mr-2"}),"Export CSV"]}),(0,s.jsxs)("button",{onClick:S,className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),(0,s.jsx)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Search"}),(0,s.jsx)("input",{type:"text",value:v,onChange:e=>w(e.target.value),placeholder:"Search user, email, mobile, or description...",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Type"}),(0,s.jsxs)("select",{value:g,onChange:e=>f(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"",children:"All Types"}),(0,s.jsx)("option",{value:"video_earning",children:"Video Earning"}),(0,s.jsx)("option",{value:"withdrawal",children:"Withdrawal"}),(0,s.jsx)("option",{value:"bonus",children:"Bonus"}),(0,s.jsx)("option",{value:"referral",children:"Referral"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,s.jsxs)("select",{value:y,onChange:e=>b(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"",children:"All Status"}),(0,s.jsx)("option",{value:"completed",children:"Completed"}),(0,s.jsx)("option",{value:"pending",children:"Pending"}),(0,s.jsx)("option",{value:"failed",children:"Failed"}),(0,s.jsx)("option",{value:"cancelled",children:"Cancelled"})]})]}),(0,s.jsx)("div",{className:"flex items-end",children:(0,s.jsx)("button",{onClick:()=>{f(""),b(""),w(""),j(1)},className:"w-full bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg",children:"Clear Filters"})})]})}),(0,s.jsx)("div",{className:"p-6",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Mobile"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Description"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:q.map(e=>(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.userName}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.userEmail})]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("div",{className:"text-sm text-gray-900",children:e.userMobile})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("i",{className:`${E(e.type)} mr-2`}),(0,s.jsx)("span",{className:"text-sm text-gray-900",children:_(e.type)})]})}),(0,s.jsx)("td",{className:"px-6 py-4",children:(0,s.jsx)("div",{className:"text-sm text-gray-900",children:e.description})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("span",{className:`text-sm font-medium ${e.amount>0?"text-green-600":"text-red-600"}`,children:[e.amount>0?"+":"",A(e.amount)]})}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:[e.date.toLocaleDateString()," ",e.date.toLocaleTimeString()]}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${"completed"===e.status?"bg-green-100 text-green-800":"pending"===e.status?"bg-yellow-100 text-yellow-800":"failed"===e.status?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})})]},e.id))})]})}),D>1&&(0,s.jsx)("div",{className:"bg-white px-4 py-3 border-t border-gray-200 sm:px-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"text-sm text-gray-700",children:["Showing ",k+1," to ",Math.min(k+20,C.length)," of ",C.length," results"]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)("button",{onClick:()=>j(e=>Math.max(1,e-1)),disabled:1===N,className:"px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded disabled:opacity-50",children:"Previous"}),(0,s.jsxs)("span",{className:"px-3 py-1 text-sm bg-blue-500 text-white rounded",children:[N," of ",D]}),(0,s.jsx)("button",{onClick:()=>j(e=>Math.min(D,e+1)),disabled:N===D,className:"px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded disabled:opacity-50",children:"Next"})]})]})})]})})]})}},41792:e=>{"use strict";e.exports=require("node:querystring")},51278:(e,t,r)=>{"use strict";r.d(t,{M4:()=>o,_f:()=>n});var s=r(33784),a=r(77567);function i(e){try{Object.keys(localStorage).forEach(t=>{(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function n(e,t="/login"){try{if((await a.A.fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&i(e),await s.j2.signOut(),a.A.fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),a.A.fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function o(e,t="/login"){try{e&&i(e),await s.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65221:(e,t,r)=>{Promise.resolve().then(r.bind(r,8574))},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78373:(e,t,r)=>{Promise.resolve().then(r.bind(r,41772))},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83475:(e,t,r)=>{"use strict";function s(e,t,r){if(!e||0===e.length)return void alert("No data to export");let s=r||Object.keys(e[0]),a=new Blob([[s.join(","),...e.map(e=>s.map(t=>{let r=e[t];if(null==r)return"";if("string"==typeof r){let e=r.replace(/"/g,'""');return e.includes(",")?`"${e}"`:e}return r instanceof Date?r.toLocaleDateString():String(r)}).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),i=document.createElement("a");if(void 0!==i.download){let e=URL.createObjectURL(a);i.setAttribute("href",e),i.setAttribute("download",`${t}_${new Date().toISOString().split("T")[0]}.csv`),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i)}}function a(e){return e.map(e=>({"User ID":e.id||"",Name:e.name,Email:e.email,Mobile:e.mobile,"Referral Code":e.referralCode,"Referred By":e.referredBy||"Direct",Plan:e.plan,"Plan Expiry":e.planExpiry?.toLocaleDateString()||"","Active Days":e.activeDays,"Total Videos":e.totalVideos,"Today Videos":e.todayVideos,"Last Video Date":e.lastVideoDate?.toLocaleDateString()||"","Video Duration (seconds)":e.videoDuration||300,"Quick Video Advantage":e.quickVideoAdvantage?"Yes":"No","Quick Video Advantage Expiry":e.quickVideoAdvantageExpiry?.toLocaleDateString()||"","Quick Video Advantage Days":e.quickVideoAdvantageDays||"","Quick Video Advantage Granted By":e.quickVideoAdvantageGrantedBy||"","Wallet Balance":e.wallet||0,"Referral Bonus Credited":e.referralBonusCredited?"Yes":"No",Status:e.status,"Joined Date":e.joinedDate?.toLocaleDateString()||"","Joined Time":e.joinedDate?.toLocaleTimeString()||""}))}function i(e){return e.map(e=>({"User ID":e.userId,"User Name":e.userName||"","User Email":e.userEmail||"","User Mobile":e.userMobile||"",Type:e.type,Amount:e.amount,Description:e.description,Status:e.status,Date:e.date?.toLocaleDateString()||"",Time:e.date?.toLocaleTimeString()||""}))}function n(e){return e.map(e=>({"User ID":e.userId||"","User Name":e.userName,"User Email":e.userEmail,"Mobile Number":e.userMobile||"","User Plan":e.userPlan||"","Active Days":e.userActiveDays||0,"Wallet Balance":e.walletBalance||0,"Withdrawal Amount":e.amount,"Account Holder Name":e.bankDetails?.accountHolderName||"","Bank Name":e.bankDetails?.bankName||"","Account Number":e.bankDetails?.accountNumber||"","IFSC Code":e.bankDetails?.ifscCode||"",Status:e.status,"Request Date":e.requestDate?.toLocaleDateString()||"","Request Time":e.requestDate?.toLocaleTimeString()||"","Admin Notes":e.adminNotes||""}))}function o(e){return e.map(e=>({Title:e.title,Message:e.message,Type:e.type,Target:e.target,Status:e.status,"Created Date":e.createdAt?.toLocaleDateString()||"","Sent Date":e.sentAt?.toLocaleDateString()||""}))}r.d(t,{Bf:()=>s,Fz:()=>a,Pe:()=>o,dB:()=>n,sL:()=>i})},87979:(e,t,r)=>{"use strict";r.d(t,{Nu:()=>n,hD:()=>i,wC:()=>o});var s=r(43210);r(63385),r(33784);var a=r(51278);function i(){let[e,t]=(0,s.useState)(null),[r,i]=(0,s.useState)(!0),n=async()=>{try{await (0,a.M4)(e?.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:r,signOut:n}}function n(){let{user:e,loading:t}=i();return{user:e,loading:t}}function o(){let{user:e,loading:t}=i(),[r,a]=(0,s.useState)(!1),[n,o]=(0,s.useState)(!0);return{user:e,loading:t||n,isAdmin:r}}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[6204,2756,7567,8441,3582],()=>r(22245));module.exports=s})();