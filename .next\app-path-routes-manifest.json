{"/_not-found/page": "/_not-found", "/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/login/page": "/admin/login", "/admin/leaves/page": "/admin/leaves", "/admin/settings/page": "/admin/settings", "/admin/notifications/page": "/admin/notifications", "/admin/page": "/admin", "/admin/transactions/page": "/admin/transactions", "/admin/setup/page": "/admin/setup", "/admin/users/page": "/admin/users", "/clear-cache/page": "/clear-cache", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/upload-users/page": "/admin/upload-users", "/dashboard/page": "/dashboard", "/admin/withdrawals/page": "/admin/withdrawals", "/debug-firestore-issue/page": "/debug-firestore-issue", "/debug-firestore/page": "/debug-firestore", "/debug-registration-simple/page": "/debug-registration-simple", "/forgot-password/page": "/forgot-password", "/debug-registration/page": "/debug-registration", "/login/page": "/login", "/page": "/", "/plans/page": "/plans", "/profile/page": "/profile", "/reset-password/page": "/reset-password", "/refer/page": "/refer", "/register/page": "/register", "/support/page": "/support", "/registration-diagnostics/page": "/registration-diagnostics", "/test-firebase-connection/page": "/test-firebase-connection", "/test-firestore/page": "/test-firestore", "/test-firebase/page": "/test-firebase", "/test-registration/page": "/test-registration", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/test-reg-simple/page": "/test-reg-simple", "/test-simple-registration/page": "/test-simple-registration", "/transactions/page": "/transactions", "/test-videos/page": "/test-videos", "/wallet/page": "/wallet", "/work/page": "/work"}