{"/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/leaves/page": "/admin/leaves", "/admin/page": "/admin", "/admin/settings/page": "/admin/settings", "/admin/setup/page": "/admin/setup", "/admin/transactions/page": "/admin/transactions", "/admin/notifications/page": "/admin/notifications", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/users/page": "/admin/users", "/admin/login/page": "/admin/login", "/_not-found/page": "/_not-found", "/admin/upload-users/page": "/admin/upload-users", "/clear-cache/page": "/clear-cache", "/dashboard/page": "/dashboard", "/admin/withdrawals/page": "/admin/withdrawals", "/debug-firestore-issue/page": "/debug-firestore-issue", "/debug-firestore/page": "/debug-firestore", "/debug-registration-simple/page": "/debug-registration-simple", "/forgot-password/page": "/forgot-password", "/debug-registration/page": "/debug-registration", "/login/page": "/login", "/plans/page": "/plans", "/page": "/", "/profile/page": "/profile", "/refer/page": "/refer", "/register/page": "/register", "/registration-diagnostics/page": "/registration-diagnostics", "/reset-password/page": "/reset-password", "/support/page": "/support", "/test-firebase-connection/page": "/test-firebase-connection", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/test-firebase/page": "/test-firebase", "/test-firestore/page": "/test-firestore", "/test-registration/page": "/test-registration", "/test-reg-simple/page": "/test-reg-simple", "/test-simple-registration/page": "/test-simple-registration", "/transactions/page": "/transactions", "/test-videos/page": "/test-videos", "/wallet/page": "/wallet", "/work/page": "/work"}