"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6779],{6779:(t,a,e)=>{e.d(a,{Ki:()=>l,Pn:()=>n,TK:()=>w,getWithdrawals:()=>c,hG:()=>E,lo:()=>i,updateWithdrawalStatus:()=>u});var r=e(5317),d=e(6104),o=e(3592);let s=new Map;async function n(){let t="dashboard-stats",a=function(t){let a=s.get(t);return a&&Date.now()-a.timestamp<3e5?a.data:null}(t);if(a)return a;try{let a=new Date;a.setHours(0,0,0,0);let e=r.Dc.fromDate(a),n=await (0,r.GG)((0,r.rJ)(d.db,o.COLLECTIONS.users)),i=n.size,l=(0,r.P)((0,r.rJ)(d.db,o.COLLECTIONS.users),(0,r._M)(o.FIELD_NAMES.joinedDate,">=",e)),c=(await (0,r.GG)(l)).size,w=0,E=0,u=0,h=0;n.forEach(t=>{var e;let r=t.data();w+=r[o.FIELD_NAMES.totalVideos]||0,E+=r[o.FIELD_NAMES.wallet]||0;let d=null==(e=r[o.FIELD_NAMES.lastVideoDate])?void 0:e.toDate();d&&d.toDateString()===a.toDateString()&&(u+=r[o.FIELD_NAMES.todayVideos]||0)});try{let t=(0,r.P)((0,r.rJ)(d.db,o.COLLECTIONS.transactions),(0,r._M)(o.FIELD_NAMES.type,"==","video_earning"),(0,r.AB)(1e3));(await (0,r.GG)(t)).forEach(t=>{var e;let r=t.data(),d=null==(e=r[o.FIELD_NAMES.date])?void 0:e.toDate();d&&d>=a&&(h+=r[o.FIELD_NAMES.amount]||0)})}catch(t){console.warn("Could not fetch today's transactions:",t)}let L=(0,r.P)((0,r.rJ)(d.db,o.COLLECTIONS.withdrawals),(0,r._M)("status","==","pending")),D=(await (0,r.GG)(L)).size,p=(0,r.P)((0,r.rJ)(d.db,o.COLLECTIONS.withdrawals),(0,r._M)("date",">=",e)),N=(await (0,r.GG)(p)).size,S={totalUsers:i,totalVideos:w,totalEarnings:E,pendingWithdrawals:D,todayUsers:c,todayVideos:u,todayEarnings:h,todayWithdrawals:N};return s.set(t,{data:S,timestamp:Date.now()}),S}catch(t){throw console.error("Error getting admin dashboard stats:",t),t}}async function i(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let e=(0,r.P)((0,r.rJ)(d.db,o.COLLECTIONS.users),(0,r.My)(o.FIELD_NAMES.joinedDate,"desc"),(0,r.AB)(t));a&&(e=(0,r.P)((0,r.rJ)(d.db,o.COLLECTIONS.users),(0,r.My)(o.FIELD_NAMES.joinedDate,"desc"),(0,r.HM)(a),(0,r.AB)(t)));let s=await (0,r.GG)(e);return{users:s.docs.map(t=>{var a,e;return{id:t.id,...t.data(),joinedDate:null==(a=t.data()[o.FIELD_NAMES.joinedDate])?void 0:a.toDate(),planExpiry:null==(e=t.data()[o.FIELD_NAMES.planExpiry])?void 0:e.toDate()}}),lastDoc:s.docs[s.docs.length-1]||null,hasMore:s.docs.length===t}}catch(t){throw console.error("Error getting users:",t),t}}async function l(t){try{let a=(0,r.P)((0,r.rJ)(d.db,o.COLLECTIONS.users),(0,r._M)(o.FIELD_NAMES.mobile,"==",t));return(await (0,r.GG)(a)).docs.map(t=>{var a,e;return{id:t.id,...t.data(),joinedDate:null==(a=t.data()[o.FIELD_NAMES.joinedDate])?void 0:a.toDate(),planExpiry:null==(e=t.data()[o.FIELD_NAMES.planExpiry])?void 0:e.toDate()}})}catch(t){throw console.error("Error searching users by mobile:",t),t}}async function c(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let e=(0,r.P)((0,r.rJ)(d.db,o.COLLECTIONS.withdrawals),(0,r.My)("date","desc"),(0,r.AB)(t));a&&(e=(0,r.P)((0,r.rJ)(d.db,o.COLLECTIONS.withdrawals),(0,r.My)("date","desc"),(0,r.HM)(a),(0,r.AB)(t)));let s=await (0,r.GG)(e);return{withdrawals:s.docs.map(t=>{var a;return{id:t.id,...t.data(),date:null==(a=t.data().date)?void 0:a.toDate()}}),lastDoc:s.docs[s.docs.length-1]||null,hasMore:s.docs.length===t}}catch(t){throw console.error("Error getting withdrawals:",t),t}}async function w(t,a){try{await (0,r.mZ)((0,r.H9)(d.db,o.COLLECTIONS.users,t),a),s.delete("dashboard-stats")}catch(t){throw console.error("Error updating user:",t),t}}async function E(t){try{await (0,r.kd)((0,r.H9)(d.db,o.COLLECTIONS.users,t)),s.delete("dashboard-stats")}catch(t){throw console.error("Error deleting user:",t),t}}async function u(t,a,n){try{let i=await (0,r.x7)((0,r.H9)(d.db,o.COLLECTIONS.withdrawals,t));if(!i.exists())throw Error("Withdrawal not found");let{userId:l,amount:c,status:w}=i.data(),E={status:a,updatedAt:r.Dc.now()};if(n&&(E.adminNotes=n),await (0,r.mZ)((0,r.H9)(d.db,o.COLLECTIONS.withdrawals,t),E),"approved"===a&&"approved"!==w){let{addTransaction:t}=await Promise.resolve().then(e.bind(e,3592));await t(l,{type:"withdrawal_approved",amount:0,description:"Withdrawal approved - ₹".concat(c," processed for transfer")})}if("rejected"===a&&"rejected"!==w){let{updateWalletBalance:t,addTransaction:a}=await Promise.resolve().then(e.bind(e,3592));await t(l,c),await a(l,{type:"withdrawal_rejected",amount:c,description:"Withdrawal rejected - ₹".concat(c," credited back to wallet")})}s.delete("dashboard-stats")}catch(t){throw console.error("Error updating withdrawal status:",t),t}}}}]);