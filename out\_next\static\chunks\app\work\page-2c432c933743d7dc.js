(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4246,9567],{2040:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>v});var o=a(5155),r=a(2115),n=a(6874),s=a.n(n),c=a(6681),l=a(7460),i=a(6572),d=a(3592),u=a(3631),h=a(9567),m=a(8647),g=a(4752),f=a.n(g);function v(){let{user:e,loading:t}=(0,c.Nu)(),{hasBlockingNotifications:n,isChecking:g,markAllAsRead:v}=(0,l.J)((null==e?void 0:e.uid)||null),{isBlocked:x,leaveStatus:w,checkLeaveStatus:b}=(0,i.l)({userId:(null==e?void 0:e.uid)||null,checkInterval:3e4,enabled:!!e}),[p,y]=(0,r.useState)(null),[S,N]=(0,r.useState)(0),[j,k]=(0,r.useState)(0),[D,E]=(0,r.useState)(50),[_,I]=(0,r.useState)(!1),[A,C]=(0,r.useState)(0),[L,T]=(0,r.useState)(!1),[B,M]=(0,r.useState)(!1),[V,O]=(0,r.useState)(!1),[P,U]=(0,r.useState)(0),[R,W]=(0,r.useState)([]),[G,H]=(0,r.useState)([]),[F,J]=(0,r.useState)(!1),[Q,q]=(0,r.useState)([]),[Z,X]=(0,r.useState)(0),[Y,z]=(0,r.useState)(!0),[K,$]=(0,r.useState)({totalVideos:0,currentBatch:0,totalBatches:0,videosInCurrentBatch:0}),[ee,et]=(0,r.useState)({videoDuration:300,earningPerBatch:10,plan:"Trial",hasQuickAdvantage:!1,quickAdvantageExpiry:null}),[ea,eo]=(0,r.useState)(null),[er,en]=(0,r.useState)(0),[es,ec]=(0,r.useState)(0),[el,ei]=(0,r.useState)(!0),[ed,eu]=(0,r.useState)(!1),[eh,em]=(0,r.useState)(0),eg=(0,r.useRef)(null),ef=(0,r.useRef)(null);(0,r.useEffect)(()=>{e&&ev()},[e]),(0,r.useEffect)(()=>{console.log("\uD83D\uDD0D Work page leave status check:",{isLeaveBlocked:x,leaveStatus:w,user:null==e?void 0:e.uid}),x&&w.reason&&(console.log("\uD83D\uDEAB Work blocked due to leave:",w.reason),_&&eN(),f().fire({icon:"warning",title:"Work Suspended",text:w.reason,confirmButtonText:"Go to Dashboard",allowOutsideClick:!1,allowEscapeKey:!1}).then(()=>{window.location.href="/dashboard"}))},[x,w,_]);let ev=async()=>{try{console.log("\uD83D\uDD0D Checking work access for user:",e.uid);let t=await (0,d.YG)(e.uid);if(console.log("\uD83D\uDCC5 Plan status result:",t),t.expired){console.log("\uD83D\uDEAB Work access blocked - Plan expired:",t.reason),f().fire({icon:"error",title:"Plan Expired",html:'\n            <div class="text-center">\n              <p class="mb-3">'.concat(t.reason,'</p>\n              <p class="text-sm text-gray-600">\n                Active Days: ').concat(t.activeDays||0," | Days Left: ").concat(t.daysLeft||0,"\n              </p>\n            </div>\n          "),confirmButtonText:"Upgrade Plan",showCancelButton:!0,cancelButtonText:"Go to Dashboard"}).then(e=>{e.isConfirmed?window.location.href="/plans":window.location.href="/dashboard"});return}let{debugAdminLeaveStatus:o}=await Promise.resolve().then(a.bind(a,9567));await o();let r=await (0,h.q8)(e.uid);if(console.log("\uD83D\uDCCA Work status result:",r),r.blocked){console.log("\uD83D\uDEAB Work access blocked:",r.reason),f().fire({icon:"warning",title:"Work Not Available",text:r.reason||"Work is currently blocked.",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});return}console.log("✅ Work access allowed, proceeding with normal loading"),ex(),ew(),eb(),ey(),ep()}catch(e){console.error("❌ Error checking work access (allowing work to proceed):",e),ex(),ew(),eb(),ey(),ep()}};(0,r.useEffect)(()=>{T(P>=50)},[P]),(0,r.useEffect)(()=>{let e=e=>{if(_)return e.preventDefault(),""};return window.addEventListener("beforeunload",e),()=>window.removeEventListener("beforeunload",e)},[_]),(0,r.useEffect)(()=>{let e=()=>{let e=!document.hidden;ei(e),_&&(e?ed&&(eu(!1),C(eh),eg.current=setInterval(()=>{C(e=>e<=1?(O(!0),eg.current&&clearInterval(eg.current),0):e-1)},1e3),console.log("✅ Timer resumed - User returned to page/tab")):(eu(!0),em(A),eg.current&&(clearInterval(eg.current),eg.current=null),console.log("⚠️ Timer paused - User left the page/tab")))},t=()=>{_&&ed&&e()},a=()=>{_&&!ed&&e()};return document.addEventListener("visibilitychange",e),window.addEventListener("focus",t),window.addEventListener("blur",a),()=>{document.removeEventListener("visibilitychange",e),window.removeEventListener("focus",t),window.removeEventListener("blur",a)}},[_,ed,A,eh]);let ex=async()=>{try{let t=await (0,d.Pp)(e.uid);N(t.todayVideos),k(t.totalVideos),E(Math.max(0,50-t.todayVideos))}catch(e){console.error("Error loading video data:",e)}},ew=async()=>{try{let t=await (0,d.Q6)(e.uid);et({videoDuration:t.videoDuration,earningPerBatch:t.earningPerBatch,plan:t.plan,hasQuickAdvantage:t.hasQuickAdvantage||!1,quickAdvantageExpiry:t.quickAdvantageExpiry||null})}catch(e){console.error("Error loading video settings:",e)}},eb=async()=>{try{let t=await (0,d.getUserData)(e.uid);if(eo(t),t){let a=await (0,d.YG)(e.uid);en(a.daysLeft||0),ec(a.activeDays||0),console.log("\uD83D\uDCCA Plan status loaded:",{plan:t.plan,expired:a.expired,daysLeft:a.daysLeft,activeDays:a.activeDays,reason:a.reason})}}catch(e){console.error("Error loading user data:",e)}},ep=()=>{let t=new Date().toDateString(),a="video_session_".concat(e.uid,"_").concat(t),o="watch_times_".concat(e.uid,"_").concat(t),r="daily_watch_times_".concat(e.uid,"_").concat(t),n=localStorage.getItem(a),s=localStorage.getItem(o),c=localStorage.getItem(r);if(n&&U(parseInt(n)),s)try{let e=JSON.parse(s).map(e=>new Date(e));W(e)}catch(e){console.error("Error parsing saved watch times:",e),W([])}if(c)try{let e=JSON.parse(c).map(e=>new Date(e));H(e)}catch(e){console.error("Error parsing saved daily watch times:",e),H([])}J(!0)},ey=async()=>{try{var t;z(!0);let a=await (0,u.ZB)();q(a);let o=eS(a.length);a.length>0&&(y(a[o]),X(o));let r=(0,u.No)();$(r),console.log("Loaded batch ".concat(r.currentBatch+1,"/").concat(r.totalBatches," with ").concat(a.length," videos")),console.log("Starting with video ".concat(o+1,"/").concat(a.length,": ").concat(null==(t=a[o])?void 0:t.title)),localStorage.getItem("video_change_notification_".concat(e.uid))||setTimeout(()=>{f().fire({icon:"info",title:"\uD83C\uDFAC Video Variety Feature",html:'\n              <div class="text-left">\n                <p class="mb-2">\uD83D\uDD04 <strong>Refresh to change videos!</strong></p>\n                <p class="mb-2">• Each refresh loads a different video</p>\n                <p class="mb-2">• Click "Change Video" button anytime</p>\n                <p>• Enjoy variety while earning!</p>\n              </div>\n            ',confirmButtonText:"Got it!",timer:8e3,timerProgressBar:!0}),localStorage.setItem("video_change_notification_".concat(e.uid),"shown")},2e3)}catch(a){console.error("Error initializing videos:",a);let e=[{id:"1",title:"Sample Video 1",url:"https://www.youtube.com/watch?v=dQw4w9WgXcQ",embedUrl:"https://www.youtube.com/embed/dQw4w9WgXcQ",duration:300},{id:"2",title:"Sample Video 2",url:"https://www.youtube.com/watch?v=9bZkp7q19f0",embedUrl:"https://www.youtube.com/embed/9bZkp7q19f0",duration:300},{id:"3",title:"Sample Video 3",url:"https://www.youtube.com/watch?v=L_jWHffIx5E",embedUrl:"https://www.youtube.com/embed/L_jWHffIx5E",duration:300},{id:"4",title:"Sample Video 4",url:"https://www.youtube.com/watch?v=fJ9rUzIMcZQ",embedUrl:"https://www.youtube.com/embed/fJ9rUzIMcZQ",duration:300},{id:"5",title:"Sample Video 5",url:"https://www.youtube.com/watch?v=ZZ5LpwO-An4",embedUrl:"https://www.youtube.com/embed/ZZ5LpwO-An4",duration:300}],t=eS(e.length);q(e),y(e[t]),X(t),f().fire({icon:"warning",title:"Video Loading Issue",text:"Using sample videos. Please check your internet connection.",timer:3e3,showConfirmButton:!1})}finally{z(!1)}},eS=t=>{if(t<=1)return 0;let a=Date.now()+Math.random(),o=new Date().toDateString(),r="video_refresh_".concat(e.uid,"_").concat(o),n=parseInt(localStorage.getItem(r)||"0");n+=1,localStorage.setItem(r,n.toString());let s=Math.floor(n*a%t);return console.log("Refresh #".concat(n," - Selected video index: ").concat(s)),s},eN=()=>{I(!1),C(0),O(!1),eu(!1),em(0),ef.current&&p&&(ef.current.src=p.embedUrl),eg.current&&(clearInterval(eg.current),eg.current=null)},ej=async()=>{if(L&&!B&&!(P<50)){if(x)return void f().fire({icon:"warning",title:"Submission Not Available",text:w.reason||"Video submission is not available due to leave.",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});try{M(!0);let t=ee.earningPerBatch;for(let t=0;t<50;t++)await (0,d.yx)(e.uid);await (0,d.updateWalletBalance)(e.uid,t),await (0,d.addTransaction)(e.uid,{type:"video_earning",amount:t,description:"Batch completion reward - 50 videos watched"});let a=S+50;N(a),k(j+50),E(Math.max(0,50-a));let o=new Date().toDateString(),r="video_session_".concat(e.uid,"_").concat(o),n="watch_times_".concat(e.uid,"_").concat(o);localStorage.removeItem(r),localStorage.removeItem(n),U(0),W([]),T(!1),eN(),f().fire({icon:"success",title:"\uD83C\uDF89 Congratulations!",text:"You earned ₹".concat(t," for completing a batch of 50 videos! Your wallet has been updated."),timer:4e3,showConfirmButton:!1})}catch(e){console.error("Error submitting videos:",e),f().fire({icon:"error",title:"Submission Failed",text:"There was an error submitting your videos. Please try again."})}finally{M(!1)}}},ek=e=>{let t=Math.floor(e/60);return"".concat(t,":").concat((e%60).toString().padStart(2,"0"))},eD=e=>{let t=Math.floor((new Date().getTime()-e.getTime())/6e4);if(t<1)return"Just now";if(t<60)return"".concat(t," min ago");let a=Math.floor(t/60);if(a<24)return"".concat(a,"h ago");let o=Math.floor(a/24);return"".concat(o,"d ago")};return t||Y||g?(0,o.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("div",{className:"spinner mb-4"}),(0,o.jsx)("p",{className:"text-white",children:t?"Loading...":g?"Checking notifications...":"Loading videos..."})]})}):n&&e?(0,o.jsx)(m.A,{userId:e.uid,onAllRead:v}):(0,o.jsxs)("div",{className:"min-h-screen p-4",children:[(0,o.jsxs)("header",{className:"glass-card p-4 mb-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,o.jsxs)(s(),{href:"/dashboard",className:"glass-button px-4 py-2 text-white",children:[(0,o.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,o.jsx)("h1",{className:"text-xl font-bold text-white",children:"Watch Videos & Earn"}),(0,o.jsxs)("div",{className:"text-white text-right",children:[(0,o.jsxs)("p",{className:"text-sm",children:["Plan: ",ee.plan]}),(0,o.jsxs)("p",{className:"text-sm",children:["₹",ee.earningPerBatch,"/batch (50 videos)"]})]})]}),(0,o.jsx)("div",{className:"bg-blue-500/20 border border-blue-400/30 rounded-lg p-3 mb-4",children:(0,o.jsxs)("div",{className:"flex items-center justify-center text-center",children:[(0,o.jsx)("i",{className:"fas fa-sync-alt text-blue-400 mr-2"}),(0,o.jsx)("span",{className:"text-white/90 text-sm",children:'Refresh page or click "Change Video" for different content'})]})}),(0,o.jsxs)("div",{className:"grid grid-cols-5 gap-2 text-center",children:[(0,o.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,o.jsx)("p",{className:"text-lg font-bold text-yellow-400",children:er}),(0,o.jsx)("p",{className:"text-white/80 text-xs",children:"days left"})]}),(0,o.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,o.jsx)("p",{className:"text-lg font-bold text-blue-400",children:S}),(0,o.jsx)("p",{className:"text-white/80 text-xs",children:"Today's Videos"})]}),(0,o.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,o.jsx)("p",{className:"text-lg font-bold text-green-400",children:j}),(0,o.jsx)("p",{className:"text-white/80 text-xs",children:"Total Videos"})]}),(0,o.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,o.jsx)("p",{className:"text-lg font-bold text-purple-400",children:Math.max(0,50-S)}),(0,o.jsx)("p",{className:"text-white/80 text-xs",children:"Videos Left"})]}),(0,o.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,o.jsxs)("p",{className:"text-lg font-bold text-orange-400",children:[es,"/","Trial"===ee.plan?"2":"30"]}),(0,o.jsx)("p",{className:"text-white/80 text-xs",children:"Active Days"})]})]})]}),(0,o.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,o.jsxs)("h2",{className:"text-xl font-bold text-white",children:[(0,o.jsx)("i",{className:"fas fa-play-circle mr-2"}),"Watch Video & Earn"]}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[ee.hasQuickAdvantage&&(0,o.jsxs)("div",{className:"bg-green-500/20 border border-green-400/30 rounded-lg px-3 py-1",children:[(0,o.jsxs)("div",{className:"flex items-center text-green-300 text-sm",children:[(0,o.jsx)("i",{className:"fas fa-bolt mr-1"}),(0,o.jsx)("span",{className:"font-medium",children:"Quick Advantage Active"})]}),ee.quickAdvantageExpiry&&(0,o.jsxs)("div",{className:"text-xs text-green-400 mt-1",children:["Until: ",new Date(ee.quickAdvantageExpiry).toLocaleDateString()]})]}),(0,o.jsxs)("button",{onClick:()=>window.location.reload(),className:"glass-button px-3 py-1 text-white text-sm",title:"Refresh to change video",children:[(0,o.jsx)("i",{className:"fas fa-sync-alt mr-1"}),"Change Video"]})]})]}),p&&(0,o.jsxs)("div",{className:"aspect-video mb-4 video-container ".concat(_?"watching":""),children:[(0,o.jsx)("iframe",{ref:ef,src:_?"".concat(p.embedUrl,"?autoplay=1&mute=0&controls=1&rel=0&modestbranding=1&disablekb=1"):p.embedUrl,title:p.title,className:"w-full h-full rounded-lg border-0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0}),_&&(0,o.jsx)("div",{className:"video-protection-overlay rounded-lg"}),!_&&(0,o.jsx)("div",{className:"absolute inset-0 bg-black/30 backdrop-blur-sm rounded-lg flex items-center justify-center",children:(0,o.jsx)("div",{className:"text-center text-white",children:(0,o.jsx)("i",{className:"fas fa-play-circle text-6xl opacity-60 text-youtube-red"})})})]}),(0,o.jsx)("div",{className:"text-center",children:_?(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)("div",{className:"text-3xl font-bold text-white",children:ek(A)}),ed&&(0,o.jsx)("div",{className:"bg-red-500/20 border border-red-400/30 rounded-lg p-3 mb-4",children:(0,o.jsxs)("div",{className:"flex items-center justify-center text-center",children:[(0,o.jsx)("i",{className:"fas fa-pause text-red-400 mr-2"}),(0,o.jsx)("span",{className:"text-red-300 text-sm font-medium",children:"Timer Paused - Please stay on this page to continue watching"})]})}),(0,o.jsx)("div",{className:"bg-white/20 rounded-full h-3 max-w-md mx-auto",children:(0,o.jsx)("div",{className:"h-3 rounded-full transition-all duration-1000 ".concat(ed?"bg-red-500":"bg-youtube-red"),style:{width:"".concat((ee.videoDuration-A)/ee.videoDuration*100,"%")}})}),(0,o.jsxs)("div",{className:"space-x-4",children:[(0,o.jsxs)("button",{onClick:eN,className:"btn-secondary",children:[(0,o.jsx)("i",{className:"fas fa-stop mr-2"}),"Stop Watching"]}),V&&(0,o.jsx)("button",{onClick:()=>{if(!V||B)return;if(x){eN(),f().fire({icon:"warning",title:"Work Suspended",text:w.reason||"Work has been suspended due to leave.",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});return}let t=P+1,a=new Date,o=[...R,a],r=[...G,a];U(t),W(o),H(r);let n=new Date().toDateString(),s="video_session_".concat(e.uid,"_").concat(n),c="watch_times_".concat(e.uid,"_").concat(n),l="daily_watch_times_".concat(e.uid,"_").concat(n);localStorage.setItem(s,t.toString()),localStorage.setItem(c,JSON.stringify(o.map(e=>e.toISOString()))),localStorage.setItem(l,JSON.stringify(r.map(e=>e.toISOString())));let i=Z+1;if(i>=Q.length)try{let e=(0,u.CA)();q(e);let a=Math.floor(Math.random()*e.length);X(a),y(e[a]);let o=(0,u.No)();$(o),f().fire({icon:"info",title:"New Video Batch Loaded",text:"Video ".concat(t,"/50 completed. Batch ").concat(o.currentBatch+1,"/").concat(o.totalBatches," loaded."),timer:2e3,showConfirmButton:!1})}catch(t){console.error("Error loading next batch:",t);let e=Math.floor(Math.random()*Q.length);X(e),y(Q[e])}else{if(.3>Math.random()&&Q.length>3){let e=Q.map((e,t)=>t).filter(e=>e!==Z);i=e[Math.floor(Math.random()*e.length)],console.log("Randomized next video: ".concat(i," (was going to ").concat(Z+1,")"))}X(i),y(Q[i])}eN(),t<50?f().fire({icon:"success",title:"Video Completed!",text:"Progress: ".concat(t,"/50 videos watched. ").concat(50-t," more to go!"),timer:2e3,showConfirmButton:!1}):f().fire({icon:"success",title:"\uD83C\uDF89 All Videos Completed!",text:'You have watched all 50 videos! Click "Submit & Earn" to get your rewards.',timer:3e3,showConfirmButton:!1})},disabled:B||!V,className:"".concat(B?"btn-disabled cursor-not-allowed opacity-50":"btn-primary"),children:B?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Processing..."]}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("i",{className:"fas fa-arrow-right mr-2"}),"Next Video"]})})]})]}):(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)("button",{onClick:()=>{if(!_){if(x)return void f().fire({icon:"warning",title:"Work Not Available",text:w.reason||"Work is currently blocked due to leave.",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});if(D<=0)return void f().fire({icon:"warning",title:"Daily Limit Reached",text:"You have reached your daily video limit. Come back tomorrow!"});if(I(!0),C(ee.videoDuration),O(!1),eu(!1),em(0),ef.current&&p){let e="".concat(p.embedUrl,"?autoplay=1&mute=0&controls=1&rel=0&modestbranding=1");ef.current.src=e}el?eg.current=setInterval(()=>{C(e=>e<=1?(O(!0),eg.current&&clearInterval(eg.current),0):e-1)},1e3):(eu(!0),em(ee.videoDuration))}},disabled:P>=50||_||B,className:"text-lg px-8 py-4 ".concat(P>=50||_||B?"btn-disabled cursor-not-allowed opacity-50":ee.hasQuickAdvantage?"btn-success bg-green-500 hover:bg-green-600":"btn-primary"),children:_?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Starting Video..."]}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("i",{className:"mr-2 ".concat(ee.hasQuickAdvantage?"fas fa-bolt":"fas fa-play")}),ee.hasQuickAdvantage?"Quick Watch":"Start Watching"," (",ek(ee.videoDuration),")"]})}),L&&P>=50&&(0,o.jsx)("button",{onClick:ej,disabled:B,className:"btn-success text-lg px-8 py-4 bg-green-500 hover:bg-green-600",children:B?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Submitting All Videos..."]}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("i",{className:"fas fa-trophy mr-2"}),"Submit & Earn ₹",ee.earningPerBatch]})})]})})]}),G.length>0&&(0,o.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,o.jsxs)("h2",{className:"text-xl font-bold text-white",children:[(0,o.jsx)("i",{className:"fas fa-clock mr-2"}),"Today's Watch History"]}),(0,o.jsxs)("div",{className:"text-white/70 text-sm",children:["Total: ",G.length," videos watched"]})]}),(0,o.jsx)("div",{className:"max-h-64 overflow-y-auto",children:(0,o.jsx)("div",{className:"grid gap-2",children:G.map((e,t)=>(0,o.jsxs)("div",{className:"bg-white/10 rounded-lg p-3 flex items-center justify-between",children:[(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("div",{className:"bg-youtube-red/20 rounded-full p-2 mr-3",children:(0,o.jsx)("i",{className:"fas fa-play text-youtube-red text-sm"})}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("p",{className:"text-white font-medium",children:["Video #",t+1]}),(0,o.jsx)("p",{className:"text-white/70 text-sm",children:e.toLocaleDateString("en-IN",{weekday:"short",year:"numeric",month:"short",day:"numeric"})})]})]}),(0,o.jsxs)("div",{className:"text-right",children:[(0,o.jsx)("p",{className:"text-white font-medium",children:e.toLocaleTimeString("en-IN",{hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!0})}),(0,o.jsx)("p",{className:"text-white/70 text-xs",children:eD(e)})]})]},t))})}),G.length>=50&&(0,o.jsx)("div",{className:"mt-4 bg-green-500/20 border border-green-400/30 rounded-lg p-3",children:(0,o.jsxs)("div",{className:"flex items-center justify-center text-center",children:[(0,o.jsx)("i",{className:"fas fa-trophy text-green-400 mr-2"}),(0,o.jsx)("span",{className:"text-green-300 text-sm font-medium",children:"Daily target completed! Great job! \uD83C\uDF89"})]})})]})]})}},3631:(e,t,a)=>{"use strict";a.d(t,{CA:()=>i,No:()=>d,ZB:()=>m,iD:()=>u,tx:()=>h});let o={CURRENT_BATCH:"mytube_current_batch",BATCH_PREFIX:"mytube_batch_",VIDEO_INDEX:"mytube_video_index",TOTAL_VIDEOS:"mytube_total_videos",LAST_PROCESSED:"mytube_last_processed"};function r(e){for(let t of[/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,/youtube\.com\/v\/([^&\n?#]+)/]){let a=e.match(t);if(a)return a[1]}return null}function n(e){let t=r(e);return t?"https://www.youtube.com/embed/".concat(t):e}function s(e,t){return"Video ".concat(t+1)}function c(e){let t=function(e){try{let t=localStorage.getItem("".concat(o.BATCH_PREFIX).concat(e));if(!t)return null;let a=JSON.parse(t);if(Date.now()-a.lastUpdated>864e5)return localStorage.removeItem("".concat(o.BATCH_PREFIX).concat(e)),null;return a}catch(t){return console.error("Error loading batch ".concat(e,":"),t),null}}(e);return t?t.videos:[]}function l(){return c(parseInt(localStorage.getItem(o.CURRENT_BATCH)||"0"))}function i(){let e=(parseInt(localStorage.getItem(o.CURRENT_BATCH)||"0")+1)%Math.ceil(parseInt(localStorage.getItem(o.TOTAL_VIDEOS)||"0")/100);return localStorage.setItem(o.CURRENT_BATCH,e.toString()),c(e)}function d(){let e=parseInt(localStorage.getItem(o.TOTAL_VIDEOS)||"0"),t=parseInt(localStorage.getItem(o.CURRENT_BATCH)||"0"),a=Math.ceil(e/100),r=c(t);return{totalVideos:e,currentBatch:t,totalBatches:a,videosInCurrentBatch:r.length}}function u(){Object.keys(localStorage).forEach(e=>{(e.startsWith(o.BATCH_PREFIX)||Object.values(o).includes(e))&&localStorage.removeItem(e)}),console.log("Cleared all video storage")}async function h(){try{let e=await fetch("/Mytube.json");if(!e.ok)throw Error("Failed to load videos: ".concat(e.statusText));let t=await e.json();console.log("Raw video data loaded:",Object.keys(t).length,"entries");let a=[];return Array.isArray(t)?t.forEach((e,t)=>{Object.entries(e).forEach(e=>{let[t,o]=e,c=r(o);c&&a.push({id:"video_".concat(a.length,"_").concat(c),title:s(o,a.length),url:o,embedUrl:n(o),duration:300,category:"General",batchIndex:Math.floor(a.length/100)})})}):Object.entries(t).forEach((e,t)=>{let[o,c]=e,l=r(c);l&&a.push({id:"video_".concat(a.length,"_").concat(l),title:s(c,a.length),url:c,embedUrl:n(c),duration:300,category:"General",batchIndex:Math.floor(a.length/100)})}),a}catch(e){throw console.error("Error loading videos from file:",e),e}}async function m(){try{if(!function(){let e=localStorage.getItem(o.LAST_PROCESSED);return!e||Date.now()-parseInt(e)>864e5}())return console.log("Using cached video data..."),l();{console.log("Loading fresh video data...");let e=await h();return!function(e){let t=Math.ceil(e.length/100);for(let r=0;r<t;r++){let t=100*r,n=Math.min(t+100,e.length),s=e.slice(t,n);var a=r;try{let e={batchNumber:a,videos:s,totalVideos:s.length,lastUpdated:Date.now()};localStorage.setItem("".concat(o.BATCH_PREFIX).concat(a),JSON.stringify(e))}catch(e){console.error("Error saving batch ".concat(a,":"),e)}}localStorage.setItem(o.TOTAL_VIDEOS,e.length.toString()),localStorage.setItem(o.CURRENT_BATCH,"0"),localStorage.setItem(o.LAST_PROCESSED,Date.now().toString()),console.log("Saved ".concat(e.length," videos in ").concat(t," batches"))}(e),l()}}catch(t){console.error("Error initializing video system:",t);let e=l();if(e.length>0)return console.log("Using cached videos as fallback"),e;throw t}}},6572:(e,t,a)=>{"use strict";a.d(t,{l:()=>n});var o=a(2115),r=a(9567);function n(e){let{userId:t,checkInterval:a=3e4,enabled:n=!0}=e,[s,c]=(0,o.useState)({blocked:!1,lastChecked:new Date}),[l,i]=(0,o.useState)(!1),d=(0,o.useCallback)(async()=>{if(t&&n)try{i(!0);let e=await (0,r.q8)(t);return c({blocked:e.blocked,reason:e.reason,lastChecked:new Date}),e}catch(e){return console.error("Error checking leave status:",e),c(e=>({...e,lastChecked:new Date})),{blocked:!1}}finally{i(!1)}},[t,n]);return(0,o.useEffect)(()=>{t&&n&&d()},[t,n,d]),(0,o.useEffect)(()=>{if(!t||!n||a<=0)return;let e=setInterval(()=>{d()},a);return()=>clearInterval(e)},[t,n,a,d]),{leaveStatus:s,isChecking:l,checkLeaveStatus:d,isBlocked:s.blocked}}},8305:(e,t,a)=>{Promise.resolve().then(a.bind(a,2040))},9567:(e,t,a)=>{"use strict";a.d(t,{applyUserLeave:()=>u,cancelUserLeave:()=>m,createAdminLeave:()=>s,debugAdminLeaveStatus:()=>l,deleteAdminLeave:()=>i,getAdminLeaves:()=>c,getUserLeaves:()=>h,getUserMonthlyLeaveCount:()=>g,isAdminLeaveDay:()=>d,isUserOnLeave:()=>f,q8:()=>v});var o=a(6104),r=a(5317);let n={adminLeaves:"adminLeaves",userLeaves:"userLeaves"};async function s(e){try{return(await (0,r.gS)((0,r.rJ)(o.db,n.adminLeaves),{...e,date:r.Dc.fromDate(e.date),createdAt:r.Dc.now()})).id}catch(e){throw console.error("Error creating admin leave:",e),e}}async function c(){try{let e=(0,r.P)((0,r.rJ)(o.db,n.adminLeaves),(0,r.My)("date","asc")),t=(await (0,r.GG)(e)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate(),createdAt:e.data().createdAt.toDate()}));return console.log("\uD83D\uDCC5 All admin leaves:",t),t}catch(e){throw console.error("Error getting admin leaves:",e),e}}async function l(){try{let e=new Date;console.log("\uD83D\uDD0D Debug: Checking admin leave status for today:",e.toDateString());let t=await d(e);console.log("\uD83D\uDCCA Debug: Admin leave result:",t);let a=await c();console.log("\uD83D\uDCC5 Debug: All admin leaves in database:",a);let o=a.filter(t=>t.date.toDateString()===e.toDateString());console.log("\uD83D\uDCC5 Debug: Today's admin leaves:",o)}catch(e){console.error("❌ Debug: Error checking admin leave status:",e)}}async function i(e){try{await (0,r.kd)((0,r.H9)(o.db,n.adminLeaves,e))}catch(e){throw console.error("Error deleting admin leave:",e),e}}async function d(e){try{let t=new Date(e);t.setHours(0,0,0,0);let a=new Date(e);a.setHours(23,59,59,999),console.log("\uD83D\uDD0D Checking admin leave for date range:",t.toISOString(),"to",a.toISOString());let s=(0,r.P)((0,r.rJ)(o.db,n.adminLeaves),(0,r._M)("date",">=",r.Dc.fromDate(t)),(0,r._M)("date","<=",r.Dc.fromDate(a))),c=await (0,r.GG)(s),l=!c.empty;return l?console.log("\uD83D\uDCC5 Found admin leave(s) for today:",c.docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate()}))):console.log("\uD83D\uDCC5 No admin leaves found for today"),l}catch(e){return console.error("❌ Error checking admin leave day:",e),!1}}async function u(e){try{let t,a,s,c=new Date,l=c.getFullYear(),i=c.getMonth()+1,d=await g(e.userId,l,i),u="pending";return d<4&&(u="approved",t="system",s=r.Dc.now(),a="Auto-approved: ".concat(d+1,"/").concat(4," monthly leaves used")),{id:(await (0,r.gS)((0,r.rJ)(o.db,n.userLeaves),{...e,date:r.Dc.fromDate(e.date),status:u,appliedAt:r.Dc.now(),...t&&{reviewedBy:t},...s&&{reviewedAt:s},...a&&{reviewNotes:a}})).id,autoApproved:"approved"===u,usedLeaves:d+ +("approved"===u),maxLeaves:4}}catch(e){throw console.error("Error applying user leave:",e),e}}async function h(e){try{let t=(0,r.P)((0,r.rJ)(o.db,n.userLeaves),(0,r._M)("userId","==",e),(0,r.My)("date","desc"));return(await (0,r.GG)(t)).docs.map(e=>{var t;return{id:e.id,...e.data(),date:e.data().date.toDate(),appliedAt:e.data().appliedAt.toDate(),reviewedAt:null==(t=e.data().reviewedAt)?void 0:t.toDate()}})}catch(e){throw console.error("Error getting user leaves:",e),e}}async function m(e){try{await (0,r.kd)((0,r.H9)(o.db,n.userLeaves,e))}catch(e){throw console.error("Error cancelling user leave:",e),e}}async function g(e,t,a){try{let s=new Date(t,a-1,1),c=new Date(t,a,0,23,59,59,999),l=(0,r.P)((0,r.rJ)(o.db,n.userLeaves),(0,r._M)("userId","==",e),(0,r._M)("status","==","approved"),(0,r._M)("date",">=",r.Dc.fromDate(s)),(0,r._M)("date","<=",r.Dc.fromDate(c)));return(await (0,r.GG)(l)).size}catch(e){return console.error("Error getting user monthly leave count:",e),0}}async function f(e,t){try{let a=new Date(t);a.setHours(0,0,0,0);let s=new Date(t);s.setHours(23,59,59,999),console.log("\uD83D\uDD0D Checking user leave for user:",e,"on date range:",a.toISOString(),"to",s.toISOString());let c=(0,r.P)((0,r.rJ)(o.db,n.userLeaves),(0,r._M)("userId","==",e),(0,r._M)("status","==","approved"),(0,r._M)("date",">=",r.Dc.fromDate(a)),(0,r._M)("date","<=",r.Dc.fromDate(s))),l=await (0,r.GG)(c),i=!l.empty;return i?console.log("\uD83D\uDC64 Found user leave(s) for today:",l.docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate()}))):console.log("\uD83D\uDC64 No user leaves found for today"),i}catch(e){return console.error("❌ Error checking user leave day:",e),!1}}async function v(e){try{let t=new Date;console.log("\uD83D\uDD0D Checking work block status for user:",e,"on date:",t.toDateString());try{let e=await d(t);if(console.log("\uD83D\uDCC5 Admin leave check result:",e),e)return console.log("\uD83D\uDEAB Work blocked due to admin leave"),{blocked:!0,reason:"System maintenance/holiday"}}catch(e){console.error("❌ Error checking admin leave (allowing work to continue):",e)}try{let a=await f(e,t);if(console.log("\uD83D\uDC64 User leave check result:",a),a)return console.log("\uD83D\uDEAB Work blocked due to user leave"),{blocked:!0,reason:"You are on approved leave today"}}catch(e){console.error("❌ Error checking user leave (allowing work to continue):",e)}return console.log("✅ Work is not blocked"),{blocked:!1}}catch(e){return console.error("❌ Error checking work block status (allowing work to continue):",e),{blocked:!1}}}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,3592,1018,8441,1684,7358],()=>t(8305)),_N_E=e.O()}]);