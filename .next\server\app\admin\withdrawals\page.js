(()=>{var e={};e.id=581,e.ids=[581],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10952:(e,t,s)=>{Promise.resolve().then(s.bind(s,67517))},12599:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\withdrawals\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\withdrawals\\page.tsx","default")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},26239:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c={children:["",{children:["admin",{children:["withdrawals",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,12599)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\withdrawals\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\withdrawals\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/withdrawals/page",pathname:"/admin/withdrawals",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,t,s)=>{"use strict";s.d(t,{db:()=>c,j2:()=>o});var r=s(67989),a=s(63385),i=s(75535),n=s(70146);let l=(0,r.Dk)().length?(0,r.Sx)():(0,r.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),o=(0,a.xI)(l),c=(0,i.aU)(l);(0,n.c7)(l)},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},47400:(e,t,s)=>{Promise.resolve().then(s.bind(s,12599))},51278:(e,t,s)=>{"use strict";s.d(t,{M4:()=>l,_f:()=>n});var r=s(33784),a=s(77567);function i(e){try{Object.keys(localStorage).forEach(t=>{(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function n(e,t="/login"){try{if((await a.A.fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&i(e),await r.j2.signOut(),a.A.fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),a.A.fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function l(e,t="/login"){try{e&&i(e),await r.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67517:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var r=s(60687),a=s(43210),i=s(85814),n=s.n(i),l=s(87979),o=s(83475),c=s(3582),d=s(77567);function u(){let{user:e,loading:t,isAdmin:i}=(0,l.wC)(),[u,x]=(0,a.useState)([]),[m,p]=(0,a.useState)(!0),[h,g]=(0,a.useState)(""),[f,b]=(0,a.useState)(""),[y,w]=(0,a.useState)(null),[j,N]=(0,a.useState)(!1),[v,C]=(0,a.useState)([]),[k,S]=(0,a.useState)(!1),[D,A]=(0,a.useState)(""),[B,q]=(0,a.useState)(!1),P=async()=>{try{p(!0);let{getWithdrawals:e}=await s.e(1391).then(s.bind(s,91391)),{withdrawals:t}=await e(100),r=[];for(let e of t)try{if(!e.userId||!e.amount){console.warn("Withdrawal missing required fields:",e);continue}let t=await (0,c.getUserData)(e.userId),s=await (0,c.k6)(e.userId);if(t){let a=t.activeDays||0;if("Trial"===t.plan){let e=t.joinedDate||new Date,s=new Date;a=Math.floor((s.getTime()-e.getTime())/864e5)}r.push({id:e.id,userId:e.userId,userName:t.name,userEmail:t.email,userMobile:t.mobile||"",userPlan:t.plan,userActiveDays:a,walletBalance:s?.wallet||0,amount:e.amount,bankDetails:e.bankDetails||{accountHolderName:"",accountNumber:"",ifscCode:"",bankName:""},requestDate:e.date||new Date,status:e.status||"pending",adminNotes:e.adminNotes})}}catch(t){console.error(`Error loading user data for withdrawal ${e.id}:`,t)}x(r)}catch(e){console.error("Error loading withdrawals:",e),x([]),d.A.fire({icon:"error",title:"Error",text:"Failed to load withdrawals. Please try again."})}finally{p(!1)}},U=async(e,t,r)=>{try{let{updateWithdrawalStatus:a}=await s.e(1391).then(s.bind(s,91391));await a(e,t,r),x(s=>s.map(s=>s.id===e?{...s,status:t,adminNotes:r}:s)),d.A.fire({icon:"success",title:"Status Updated",text:`Withdrawal has been ${t}.`,timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error updating withdrawal status:",e),d.A.fire({icon:"error",title:"Update Failed",text:"Failed to update withdrawal status. Please try again."})}},E=e=>{d.A.fire({title:"Approve Withdrawal",text:`Approve withdrawal of ₹${e.amount} for ${e.userName}?`,icon:"question",showCancelButton:!0,confirmButtonColor:"#10b981",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Approve",cancelButtonText:"Cancel"}).then(t=>{t.isConfirmed&&U(e.id,"approved")})},M=e=>{d.A.fire({title:"Reject Withdrawal",text:"Please provide a reason for rejection:",input:"textarea",inputPlaceholder:"Enter rejection reason...",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Reject",cancelButtonText:"Cancel",inputValidator:e=>{if(!e)return"Please provide a reason for rejection"}}).then(t=>{t.isConfirmed&&U(e.id,"rejected",t.value)})},T=e=>{d.A.fire({title:"Mark as Completed",text:`Mark withdrawal of ₹${e.amount} as completed?`,icon:"question",showCancelButton:!0,confirmButtonColor:"#3b82f6",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Complete",cancelButtonText:"Cancel"}).then(t=>{t.isConfirmed&&U(e.id,"completed")})},_=e=>{v.includes(e)?(C(t=>t.filter(t=>t!==e)),S(!1)):(C(t=>[...t,e]),v.length+1===L.length&&S(!0))},R=async()=>{let e;if(0===v.length)return void d.A.fire({icon:"warning",title:"No Selection",text:"Please select at least one withdrawal to update."});if(!D)return void d.A.fire({icon:"warning",title:"No Action Selected",text:"Please select an action to perform."});let t="approved"===D?"approve":"rejected"===D?"reject":"completed"===D?"mark as completed":D;if((e="rejected"===D?await d.A.fire({title:`Bulk ${t.charAt(0).toUpperCase()+t.slice(1)}`,text:"Please provide a reason for rejection:",input:"textarea",inputPlaceholder:"Enter rejection reason...",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:`${t.charAt(0).toUpperCase()+t.slice(1)} ${v.length} withdrawals`,cancelButtonText:"Cancel",inputValidator:e=>{if(!e)return"Please provide a reason for rejection"}}):await d.A.fire({title:`Bulk ${t.charAt(0).toUpperCase()+t.slice(1)}`,text:`Are you sure you want to ${t} ${v.length} selected withdrawals?`,icon:"question",showCancelButton:!0,confirmButtonColor:"approved"===D?"#10b981":"#3b82f6",cancelButtonColor:"#6b7280",confirmButtonText:`Yes, ${t.charAt(0).toUpperCase()+t.slice(1)}`,cancelButtonText:"Cancel"})).isConfirmed)try{for(let t of(q(!0),v))await U(t,D,e.value);C([]),S(!1),A(""),d.A.fire({icon:"success",title:"Bulk Update Complete",text:`Successfully ${t}ed ${v.length} withdrawals.`,timer:3e3,showConfirmButton:!1})}catch(e){console.error("Error in bulk update:",e),d.A.fire({icon:"error",title:"Bulk Update Failed",text:"Some withdrawals could not be updated. Please try again."})}finally{q(!1)}},L=u.filter(e=>{let t=!h||e.status===h,s=!f||e.userName.toLowerCase().includes(f.toLowerCase())||e.userEmail.toLowerCase().includes(f.toLowerCase())||e.userMobile.toLowerCase().includes(f.toLowerCase());return t&&s}),O=e=>null==e||isNaN(e)?"₹0.00":new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0,maximumFractionDigits:2}).format(e),I=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"approved":return"bg-green-100 text-green-800";case"rejected":return"bg-red-100 text-red-800";case"completed":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}},$=e=>{switch(e.toLowerCase()){case"trial":default:return"bg-gray-100 text-gray-800";case"starter":return"bg-green-100 text-green-800";case"premium":return"bg-blue-100 text-blue-800";case"gold":return"bg-yellow-100 text-yellow-800";case"platinum":return"bg-purple-100 text-purple-800";case"diamond":return"bg-pink-100 text-pink-800"}};return t||m?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading withdrawals..."})]})}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(n(),{href:"/admin",className:"text-gray-500 hover:text-gray-700",children:(0,r.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Withdrawal Requests"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-gray-700",children:["Total: ",L.length,v.length>0&&(0,r.jsxs)("span",{className:"ml-2 text-blue-600 font-medium",children:["(",v.length," selected)"]})]}),(0,r.jsxs)("button",{onClick:()=>{if(0===L.length)return void d.A.fire({icon:"warning",title:"No Data",text:"No withdrawals to export."});let e=(0,o.dB)(L);(0,o.Bf)(e,"withdrawals"),d.A.fire({icon:"success",title:"Export Complete",text:`Exported ${L.length} withdrawals to CSV file.`,timer:2e3,showConfirmButton:!1})},className:"bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg",children:[(0,r.jsx)("i",{className:"fas fa-download mr-2"}),"Export CSV"]}),(0,r.jsxs)("button",{onClick:P,className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg",children:[(0,r.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),(0,r.jsx)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Search"}),(0,r.jsx)("input",{type:"text",value:f,onChange:e=>b(e.target.value),placeholder:"Search user name, email, or mobile...",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,r.jsxs)("select",{value:h,onChange:e=>g(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"",children:"All Status"}),(0,r.jsx)("option",{value:"pending",children:"Pending"}),(0,r.jsx)("option",{value:"approved",children:"Approved"}),(0,r.jsx)("option",{value:"rejected",children:"Rejected"}),(0,r.jsx)("option",{value:"completed",children:"Completed"})]})]}),(0,r.jsx)("div",{className:"flex items-end",children:(0,r.jsx)("button",{onClick:()=>{g(""),b("")},className:"w-full bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg",children:"Clear Filters"})})]})}),v.length>0&&(0,r.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mx-6 mb-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-blue-800 font-medium",children:[(0,r.jsx)("i",{className:"fas fa-check-square mr-2"}),v.length," withdrawal",v.length>1?"s":""," selected"]}),(0,r.jsxs)("select",{value:D,onChange:e=>A(e.target.value),className:"px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"",children:"Select Action"}),(0,r.jsx)("option",{value:"approved",children:"Approve Selected"}),(0,r.jsx)("option",{value:"rejected",children:"Reject Selected"}),(0,r.jsx)("option",{value:"completed",children:"Mark as Completed"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{onClick:R,disabled:!D||B,className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:B?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Processing..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-bolt mr-2"}),"Apply Action"]})}),(0,r.jsxs)("button",{onClick:()=>{C([]),S(!1),A("")},className:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg",children:[(0,r.jsx)("i",{className:"fas fa-times mr-2"}),"Clear Selection"]})]})]})}),(0,r.jsx)("div",{className:"p-6",children:(0,r.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:k,onChange:()=>{k?(C([]),S(!1)):(C(L.map(e=>e.id)),S(!0))},className:"mr-2"}),"Select All"]})}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Mobile"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Plan"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Active Days"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Wallet Balance"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Bank Details"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Request Date"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:L.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("input",{type:"checkbox",checked:v.includes(e.id),onChange:()=>_(e.id),className:"rounded"})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.userName}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.userEmail})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"text-sm text-gray-900",children:e.userMobile||"N/A"})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${$(e.userPlan)}`,children:e.userPlan})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:[e.userActiveDays," days"]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:O(e.walletBalance)})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"text-lg font-bold text-green-600",children:O(e.amount)})}),(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,r.jsx)("div",{children:e.bankDetails.accountHolderName}),(0,r.jsx)("div",{className:"text-gray-500",children:e.bankDetails.bankName}),(0,r.jsx)("div",{className:"text-gray-500",children:e.bankDetails.accountNumber.replace(/\d(?=\d{4})/g,"*")})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.requestDate.toLocaleDateString()}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${I(e.status)}`,children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2",children:[(0,r.jsx)("button",{onClick:()=>{w(e),N(!0)},className:"text-blue-600 hover:text-blue-900",children:"View"}),"pending"===e.status&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("button",{onClick:()=>E(e),className:"text-green-600 hover:text-green-900",children:"Approve"}),(0,r.jsx)("button",{onClick:()=>M(e),className:"text-red-600 hover:text-red-900",children:"Reject"})]}),"approved"===e.status&&(0,r.jsx)("button",{onClick:()=>T(e),className:"text-blue-600 hover:text-blue-900",children:"Complete"})]})]},e.id))})]})})})}),j&&y&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-bold",children:"Withdrawal Details"}),(0,r.jsx)("button",{onClick:()=>N(!1),className:"text-gray-500 hover:text-gray-700",children:(0,r.jsx)("i",{className:"fas fa-times"})})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"User"}),(0,r.jsx)("p",{className:"text-sm text-gray-900",children:y.userName}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:y.userEmail})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Mobile Number"}),(0,r.jsx)("p",{className:"text-sm text-gray-900",children:y.userMobile||"N/A"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Plan"}),(0,r.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${$(y.userPlan)}`,children:y.userPlan})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Active Days"}),(0,r.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:[y.userActiveDays," days"]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Wallet Balance"}),(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:O(y.walletBalance)})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Withdrawal Amount"}),(0,r.jsx)("p",{className:"text-lg font-bold text-green-600",children:O(y.amount)})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Bank Details"}),(0,r.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Account Holder:"})," ",y.bankDetails.accountHolderName]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Bank:"})," ",y.bankDetails.bankName]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Account Number:"})," ",y.bankDetails.accountNumber]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"IFSC Code:"})," ",y.bankDetails.ifscCode]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Status"}),(0,r.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${I(y.status)}`,children:y.status.charAt(0).toUpperCase()+y.status.slice(1)})]}),y.adminNotes&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Admin Notes"}),(0,r.jsx)("p",{className:"text-sm text-gray-900",children:y.adminNotes})]})]})]})})]})}},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83475:(e,t,s)=>{"use strict";function r(e,t,s){if(!e||0===e.length)return void alert("No data to export");let r=s||Object.keys(e[0]),a=new Blob([[r.join(","),...e.map(e=>r.map(t=>{let s=e[t];if(null==s)return"";if("string"==typeof s){let e=s.replace(/"/g,'""');return e.includes(",")?`"${e}"`:e}return s instanceof Date?s.toLocaleDateString():String(s)}).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),i=document.createElement("a");if(void 0!==i.download){let e=URL.createObjectURL(a);i.setAttribute("href",e),i.setAttribute("download",`${t}_${new Date().toISOString().split("T")[0]}.csv`),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i)}}function a(e){return e.map(e=>({Name:e.name,Email:e.email,Mobile:e.mobile,"Referral Code":e.referralCode,"Referred By":e.referredBy||"Direct",Plan:e.plan,"Active Days":e.activeDays,"Total Videos":e.totalVideos,"Today Videos":e.todayVideos,"Video Duration (seconds)":e.videoDuration||300,"Wallet Balance":e.wallet||0,Status:e.status,"Joined Date":e.joinedDate?.toLocaleDateString()||""}))}function i(e){return e.map(e=>({"User ID":e.userId,"User Name":e.userName||"","User Email":e.userEmail||"",Type:e.type,Amount:e.amount,Description:e.description,Status:e.status,Date:e.date?.toLocaleDateString()||""}))}function n(e){return e.map(e=>({"User Name":e.userName,"User Email":e.userEmail,"Mobile Number":e.userMobile||"","User Plan":e.userPlan||"","Active Days":e.userActiveDays||0,"Wallet Balance":e.walletBalance||0,"Withdrawal Amount":e.amount,"Account Holder":e.bankDetails?.accountHolderName||"","Bank Name":e.bankDetails?.bankName||"","Account Number":e.bankDetails?.accountNumber||"","IFSC Code":e.bankDetails?.ifscCode||"",Status:e.status,"Request Date":e.requestDate?.toLocaleDateString()||"","Admin Notes":e.adminNotes||""}))}function l(e){return e.map(e=>({Title:e.title,Message:e.message,Type:e.type,Target:e.target,Status:e.status,"Created Date":e.createdAt?.toLocaleDateString()||"","Sent Date":e.sentAt?.toLocaleDateString()||""}))}s.d(t,{Bf:()=>r,Fz:()=>a,Pe:()=>l,dB:()=>n,sL:()=>i})},87979:(e,t,s)=>{"use strict";s.d(t,{Nu:()=>n,hD:()=>i,wC:()=>l});var r=s(43210);s(63385),s(33784);var a=s(51278);function i(){let[e,t]=(0,r.useState)(null),[s,i]=(0,r.useState)(!0),n=async()=>{try{await (0,a.M4)(e?.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:s,signOut:n}}function n(){let{user:e,loading:t}=i();return{user:e,loading:t}}function l(){let{user:e,loading:t}=i(),[s,a]=(0,r.useState)(!1),[n,l]=(0,r.useState)(!0);return{user:e,loading:t||n,isAdmin:s}}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[6204,2756,7567,8441,3582],()=>s(26239));module.exports=r})();