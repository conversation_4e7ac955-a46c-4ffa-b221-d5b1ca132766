[{"name": "generate-buildid", "duration": 378, "timestamp": 109056368720, "id": 4, "parentId": 1, "tags": {}, "startTime": 1750066070637, "traceId": "06a5c60055a7a519"}, {"name": "load-custom-routes", "duration": 625, "timestamp": 109056369301, "id": 5, "parentId": 1, "tags": {}, "startTime": 1750066070637, "traceId": "06a5c60055a7a519"}, {"name": "create-dist-dir", "duration": 1703, "timestamp": 109056561048, "id": 6, "parentId": 1, "tags": {}, "startTime": 1750066070829, "traceId": "06a5c60055a7a519"}, {"name": "create-pages-mapping", "duration": 782, "timestamp": 109057293627, "id": 7, "parentId": 1, "tags": {}, "startTime": 1750066071562, "traceId": "06a5c60055a7a519"}, {"name": "collect-app-paths", "duration": 49152, "timestamp": 109057294522, "id": 8, "parentId": 1, "tags": {}, "startTime": 1750066071563, "traceId": "06a5c60055a7a519"}, {"name": "create-app-mapping", "duration": 96387, "timestamp": 109057343752, "id": 9, "parentId": 1, "tags": {}, "startTime": 1750066071612, "traceId": "06a5c60055a7a519"}, {"name": "public-dir-conflict-check", "duration": 76157, "timestamp": 109057450630, "id": 10, "parentId": 1, "tags": {}, "startTime": 1750066071719, "traceId": "06a5c60055a7a519"}, {"name": "generate-routes-manifest", "duration": 14660, "timestamp": 109057527730, "id": 11, "parentId": 1, "tags": {}, "startTime": 1750066071796, "traceId": "06a5c60055a7a519"}, {"name": "create-entrypoints", "duration": 231527, "timestamp": 109062045891, "id": 15, "parentId": 13, "tags": {}, "startTime": 1750066076314, "traceId": "06a5c60055a7a519"}, {"name": "generate-webpack-config", "duration": 1259080, "timestamp": 109062277929, "id": 16, "parentId": 14, "tags": {}, "startTime": 1750066076546, "traceId": "06a5c60055a7a519"}, {"name": "next-trace-entrypoint-plugin", "duration": 4413, "timestamp": 109063874990, "id": 18, "parentId": 17, "tags": {}, "startTime": 1750066078143, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 1545050, "timestamp": 109063892875, "id": 20, "parentId": 19, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1750066078161, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 2382508, "timestamp": 109063893844, "id": 21, "parentId": 19, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1750066078162, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 3577965, "timestamp": 109063894746, "id": 62, "parentId": 19, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "build-module-tsx", "duration": 426121, "timestamp": 109068765285, "id": 64, "parentId": 17, "tags": {"name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\withdrawals\\page.tsx", "layer": "rsc"}, "startTime": 1750066083033, "traceId": "06a5c60055a7a519"}, {"name": "build-module-tsx", "duration": 452520, "timestamp": 109068747627, "id": 63, "parentId": 17, "tags": {"name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\transactions\\page.tsx", "layer": "rsc"}, "startTime": 1750066083016, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5365495, "timestamp": 109063893928, "id": 22, "parentId": 19, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Fnot-found&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078162, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5365235, "timestamp": 109063894693, "id": 52, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Ftest-firebase-connection%2Fpage&name=app%2Ftest-firebase-connection%2Fpage&pagePath=private-next-app-dir%2Ftest-firebase-connection%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Ftest-firebase-connection%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5366112, "timestamp": 109063893971, "id": 23, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fadmin%2Ffix-permissions%2Fpage&name=app%2Fadmin%2Ffix-permissions%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Ffix-permissions%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Fadmin%2Ffix-permissions%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078162, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5366094, "timestamp": 109063894017, "id": 24, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fnotifications%2Fpage&name=app%2Fadmin%2Fnotifications%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fnotifications%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Fadmin%2Fnotifications%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078162, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5366089, "timestamp": 109063894042, "id": 25, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fadmin%2Ftransactions%2Fpage&name=app%2Fadmin%2Ftransactions%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Ftransactions%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Fadmin%2Ftransactions%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078162, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5366081, "timestamp": 109063894064, "id": 26, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fsetup%2Fpage&name=app%2Fadmin%2Fsetup%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fsetup%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Fadmin%2Fsetup%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078162, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5366097, "timestamp": 109063894093, "id": 27, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fsettings%2Fpage&name=app%2Fadmin%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fsettings%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Fadmin%2Fsettings%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078162, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5366094, "timestamp": 109063894113, "id": 28, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fadmin%2Flogin%2Fpage&name=app%2Fadmin%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Fadmin%2Flogin%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078162, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5365767, "timestamp": 109063894467, "id": 29, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fwithdrawals%2Fpage&name=app%2Fadmin%2Fwithdrawals%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fwithdrawals%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Fadmin%2Fwithdrawals%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5365663, "timestamp": 109063894580, "id": 31, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fleaves%2Fpage&name=app%2Fadmin%2Fleaves%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fleaves%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Fadmin%2Fleaves%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5365696, "timestamp": 109063894587, "id": 32, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fusers%2Fpage&name=app%2Fadmin%2Fusers%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fusers%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Fadmin%2Fusers%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5365703, "timestamp": 109063894593, "id": 33, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fdashboard%2Fpage&name=app%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Fdashboard%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5365709, "timestamp": 109063894598, "id": 34, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fupload-users%2Fpage&name=app%2Fadmin%2Fupload-users%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fupload-users%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Fadmin%2Fupload-users%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5365714, "timestamp": 109063894602, "id": 35, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fpage&name=app%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Fadmin%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5365724, "timestamp": 109063894611, "id": 36, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fadmin%2Ftest-blocking%2Fpage&name=app%2Fadmin%2Ftest-blocking%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Ftest-blocking%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Fadmin%2Ftest-blocking%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5365727, "timestamp": 109063894627, "id": 39, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fforgot-password%2Fpage&name=app%2Fforgot-password%2Fpage&pagePath=private-next-app-dir%2Fforgot-password%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Fforgot-password%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5365731, "timestamp": 109063894634, "id": 40, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fdebug-firestore%2Fpage&name=app%2Fdebug-firestore%2Fpage&pagePath=private-next-app-dir%2Fdebug-firestore%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Fdebug-firestore%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5365737, "timestamp": 109063894638, "id": 41, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5365742, "timestamp": 109063894643, "id": 42, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Flogin%2Fpage&name=app%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Flogin%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5365750, "timestamp": 109063894648, "id": 43, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fdebug-registration%2Fpage&name=app%2Fdebug-registration%2Fpage&pagePath=private-next-app-dir%2Fdebug-registration%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Fdebug-registration%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5365762, "timestamp": 109063894653, "id": 44, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fplans%2Fpage&name=app%2Fplans%2Fpage&pagePath=private-next-app-dir%2Fplans%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Fplans%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5365779, "timestamp": 109063894658, "id": 45, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Frefer%2Fpage&name=app%2Frefer%2Fpage&pagePath=private-next-app-dir%2Frefer%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Frefer%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5365780, "timestamp": 109063894668, "id": 47, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fsupport%2Fpage&name=app%2Fsupport%2Fpage&pagePath=private-next-app-dir%2Fsupport%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Fsupport%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5365787, "timestamp": 109063894672, "id": 48, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fprofile%2Fpage&name=app%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fprofile%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Fprofile%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5365794, "timestamp": 109063894677, "id": 49, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fregister%2Fpage&name=app%2Fregister%2Fpage&pagePath=private-next-app-dir%2Fregister%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Fregister%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5365800, "timestamp": 109063894682, "id": 50, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Freset-password%2Fpage&name=app%2Freset-password%2Fpage&pagePath=private-next-app-dir%2Freset-password%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Freset-password%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5365811, "timestamp": 109063894687, "id": 51, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Ftest-firestore%2Fpage&name=app%2Ftest-firestore%2Fpage&pagePath=private-next-app-dir%2Ftest-firestore%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Ftest-firestore%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5365810, "timestamp": 109063894699, "id": 53, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Ftest-firebase%2Fpage&name=app%2Ftest-firebase%2Fpage&pagePath=private-next-app-dir%2Ftest-firebase%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Ftest-firebase%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5365817, "timestamp": 109063894709, "id": 55, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Ftest-simple-registration%2Fpage&name=app%2Ftest-simple-registration%2Fpage&pagePath=private-next-app-dir%2Ftest-simple-registration%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Ftest-simple-registration%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5365825, "timestamp": 109063894714, "id": 56, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Ftest-registration%2Fpage&name=app%2Ftest-registration%2Fpage&pagePath=private-next-app-dir%2Ftest-registration%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Ftest-registration%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5365830, "timestamp": 109063894719, "id": 57, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Ftest-videos%2Fpage&name=app%2Ftest-videos%2Fpage&pagePath=private-next-app-dir%2Ftest-videos%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Ftest-videos%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5365835, "timestamp": 109063894724, "id": 58, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fwallet%2Fpage&name=app%2Fwallet%2Fpage&pagePath=private-next-app-dir%2Fwallet%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Fwallet%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5365836, "timestamp": 109063894733, "id": 60, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Ftransactions%2Fpage&name=app%2Ftransactions%2Fpage&pagePath=private-next-app-dir%2Ftransactions%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Ftransactions%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5365853, "timestamp": 109063894738, "id": 61, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fwork%2Fpage&name=app%2Fwork%2Fpage&pagePath=private-next-app-dir%2Fwork%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Fwork%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5366038, "timestamp": 109063894563, "id": 30, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fclear-cache%2Fpage&name=app%2Fclear-cache%2Fpage&pagePath=private-next-app-dir%2Fclear-cache%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Fclear-cache%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5365995, "timestamp": 109063894617, "id": 37, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fdebug-firestore-issue%2Fpage&name=app%2Fdebug-firestore-issue%2Fpage&pagePath=private-next-app-dir%2Fdebug-firestore-issue%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Fdebug-firestore-issue%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5366000, "timestamp": 109063894622, "id": 38, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fdebug-registration-simple%2Fpage&name=app%2Fdebug-registration-simple%2Fpage&pagePath=private-next-app-dir%2Fdebug-registration-simple%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Fdebug-registration-simple%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5365971, "timestamp": 109063894663, "id": 46, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fregistration-diagnostics%2Fpage&name=app%2Fregistration-diagnostics%2Fpage&pagePath=private-next-app-dir%2Fregistration-diagnostics%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Fregistration-diagnostics%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5365943, "timestamp": 109063894704, "id": 54, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Ftest-firebase-connectivity%2Fpage&name=app%2Ftest-firebase-connectivity%2Fpage&pagePath=private-next-app-dir%2Ftest-firebase-connectivity%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Ftest-firebase-connectivity%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "add-entry", "duration": 5365929, "timestamp": 109063894729, "id": 59, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Ftest-reg-simple%2Fpage&name=app%2Ftest-reg-simple%2Fpage&pagePath=private-next-app-dir%2Ftest-reg-simple%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Mytube%5Csrc%5Capp&appPaths=%2Ftest-reg-simple%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750066078163, "traceId": "06a5c60055a7a519"}, {"name": "build-module-tsx", "duration": 888924, "timestamp": 109072007391, "id": 549, "parentId": 17, "tags": {"name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\transactions\\page.tsx", "layer": "ssr"}, "startTime": 1750066086275, "traceId": "06a5c60055a7a519"}, {"name": "build-module-tsx", "duration": 1511025, "timestamp": 109072013141, "id": 550, "parentId": 17, "tags": {"name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\withdrawals\\page.tsx", "layer": "ssr"}, "startTime": 1750066086281, "traceId": "06a5c60055a7a519"}, {"name": "build-module-ts", "duration": 90300, "timestamp": 109076950289, "id": 551, "parentId": 17, "tags": {"name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\lib\\adminDataService.ts", "layer": "ssr"}, "startTime": 1750066091218, "traceId": "06a5c60055a7a519"}, {"name": "make", "duration": 14738088, "timestamp": 109063892149, "id": 19, "parentId": 17, "tags": {}, "startTime": 1750066078160, "traceId": "06a5c60055a7a519"}, {"name": "get-entries", "duration": 40602, "timestamp": 109078638779, "id": 553, "parentId": 552, "tags": {}, "startTime": 1750066092907, "traceId": "06a5c60055a7a519"}, {"name": "node-file-trace-plugin", "duration": 452309, "timestamp": 109078706472, "id": 554, "parentId": 552, "tags": {"traceEntryCount": "84"}, "startTime": 1750066092975, "traceId": "06a5c60055a7a519"}, {"name": "collect-traced-files", "duration": 3647, "timestamp": 109079158831, "id": 555, "parentId": 552, "tags": {}, "startTime": 1750066093427, "traceId": "06a5c60055a7a519"}, {"name": "finish-modules", "duration": 524773, "timestamp": 109078637931, "id": 552, "parentId": 18, "tags": {}, "startTime": 1750066092906, "traceId": "06a5c60055a7a519"}, {"name": "chunk-graph", "duration": 309981, "timestamp": 109079446657, "id": 557, "parentId": 556, "tags": {}, "startTime": 1750066093715, "traceId": "06a5c60055a7a519"}, {"name": "optimize-modules", "duration": 128, "timestamp": 109079757169, "id": 559, "parentId": 556, "tags": {}, "startTime": 1750066094025, "traceId": "06a5c60055a7a519"}, {"name": "optimize-chunks", "duration": 188226, "timestamp": 109079757652, "id": 560, "parentId": 556, "tags": {}, "startTime": 1750066094026, "traceId": "06a5c60055a7a519"}, {"name": "optimize-tree", "duration": 364, "timestamp": 109079946138, "id": 561, "parentId": 556, "tags": {}, "startTime": 1750066094214, "traceId": "06a5c60055a7a519"}, {"name": "optimize-chunk-modules", "duration": 93591, "timestamp": 109079946817, "id": 562, "parentId": 556, "tags": {}, "startTime": 1750066094215, "traceId": "06a5c60055a7a519"}, {"name": "optimize", "duration": 283865, "timestamp": 109079756952, "id": 558, "parentId": 556, "tags": {}, "startTime": 1750066094025, "traceId": "06a5c60055a7a519"}, {"name": "module-hash", "duration": 128456, "timestamp": 109080151534, "id": 563, "parentId": 556, "tags": {}, "startTime": 1750066094420, "traceId": "06a5c60055a7a519"}, {"name": "code-generation", "duration": 132437, "timestamp": 109080280310, "id": 564, "parentId": 556, "tags": {}, "startTime": 1750066094548, "traceId": "06a5c60055a7a519"}, {"name": "hash", "duration": 55159, "timestamp": 109080437516, "id": 565, "parentId": 556, "tags": {}, "startTime": 1750066094706, "traceId": "06a5c60055a7a519"}, {"name": "code-generation-jobs", "duration": 875, "timestamp": 109080492666, "id": 566, "parentId": 556, "tags": {}, "startTime": 1750066094761, "traceId": "06a5c60055a7a519"}, {"name": "module-assets", "duration": 1185, "timestamp": 109080493293, "id": 567, "parentId": 556, "tags": {}, "startTime": 1750066094761, "traceId": "06a5c60055a7a519"}, {"name": "create-chunk-assets", "duration": 22867, "timestamp": 109080494530, "id": 568, "parentId": 556, "tags": {}, "startTime": 1750066094763, "traceId": "06a5c60055a7a519"}, {"name": "minify-js", "duration": 315150, "timestamp": 109080752068, "id": 570, "parentId": 569, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1750066095020, "traceId": "06a5c60055a7a519"}, {"name": "minify-js", "duration": 314561, "timestamp": 109080752676, "id": 571, "parentId": 569, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1750066095021, "traceId": "06a5c60055a7a519"}, {"name": "minify-js", "duration": 314473, "timestamp": 109080752771, "id": 572, "parentId": 569, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1750066095021, "traceId": "06a5c60055a7a519"}, {"name": "minify-js", "duration": 314376, "timestamp": 109080752872, "id": 573, "parentId": 569, "tags": {"name": "../app/admin/fix-permissions/page.js", "cache": "HIT"}, "startTime": 1750066095021, "traceId": "06a5c60055a7a519"}, {"name": "minify-js", "duration": 147658, "timestamp": 109080919597, "id": 576, "parentId": 569, "tags": {"name": "../app/admin/setup/page.js", "cache": "HIT"}, "startTime": 1750066095188, "traceId": "06a5c60055a7a519"}, {"name": "minify-js", "duration": 147493, "timestamp": 109080919784, "id": 577, "parentId": 569, "tags": {"name": "../app/admin/settings/page.js", "cache": "HIT"}, "startTime": 1750066095188, "traceId": "06a5c60055a7a519"}, {"name": "minify-js", "duration": 147455, "timestamp": 109080919827, "id": 578, "parentId": 569, "tags": {"name": "../app/admin/login/page.js", "cache": "HIT"}, "startTime": 1750066095188, "traceId": "06a5c60055a7a519"}, {"name": "minify-js", "duration": 105591, "timestamp": 109080961695, "id": 580, "parentId": 569, "tags": {"name": "../app/clear-cache/page.js", "cache": "HIT"}, "startTime": 1750066095230, "traceId": "06a5c60055a7a519"}, {"name": "minify-js", "duration": 105509, "timestamp": 109080961780, "id": 581, "parentId": 569, "tags": {"name": "../app/admin/leaves/page.js", "cache": "HIT"}, "startTime": 1750066095230, "traceId": "06a5c60055a7a519"}, {"name": "minify-js", "duration": 10262, "timestamp": 109081057031, "id": 583, "parentId": 569, "tags": {"name": "../app/dashboard/page.js", "cache": "HIT"}, "startTime": 1750066095325, "traceId": "06a5c60055a7a519"}, {"name": "minify-js", "duration": 10177, "timestamp": 109081057120, "id": 584, "parentId": 569, "tags": {"name": "../app/admin/upload-users/page.js", "cache": "HIT"}, "startTime": 1750066095325, "traceId": "06a5c60055a7a519"}, {"name": "minify-js", "duration": 5278, "timestamp": 109081062023, "id": 586, "parentId": 569, "tags": {"name": "../app/admin/test-blocking/page.js", "cache": "HIT"}, "startTime": 1750066095330, "traceId": "06a5c60055a7a519"}, {"name": "minify-js", "duration": 5228, "timestamp": 109081062076, "id": 587, "parentId": 569, "tags": {"name": "../app/debug-firestore-issue/page.js", "cache": "HIT"}, "startTime": 1750066095330, "traceId": "06a5c60055a7a519"}, {"name": "minify-js", "duration": 5214, "timestamp": 109081062093, "id": 588, "parentId": 569, "tags": {"name": "../app/debug-registration-simple/page.js", "cache": "HIT"}, "startTime": 1750066095330, "traceId": "06a5c60055a7a519"}, {"name": "minify-js", "duration": 5202, "timestamp": 109081062108, "id": 589, "parentId": 569, "tags": {"name": "../app/forgot-password/page.js", "cache": "HIT"}, "startTime": 1750066095330, "traceId": "06a5c60055a7a519"}, {"name": "minify-js", "duration": 5193, "timestamp": 109081062122, "id": 590, "parentId": 569, "tags": {"name": "../app/debug-firestore/page.js", "cache": "HIT"}, "startTime": 1750066095330, "traceId": "06a5c60055a7a519"}, {"name": "minify-js", "duration": 5183, "timestamp": 109081062134, "id": 591, "parentId": 569, "tags": {"name": "../app/page.js", "cache": "HIT"}, "startTime": 1750066095330, "traceId": "06a5c60055a7a519"}, {"name": "minify-js", "duration": 5172, "timestamp": 109081062148, "id": 592, "parentId": 569, "tags": {"name": "../app/login/page.js", "cache": "HIT"}, "startTime": 1750066095330, "traceId": "06a5c60055a7a519"}, {"name": "minify-js", "duration": 5160, "timestamp": 109081062164, "id": 593, "parentId": 569, "tags": {"name": "../app/debug-registration/page.js", "cache": "HIT"}, "startTime": 1750066095330, "traceId": "06a5c60055a7a519"}, {"name": "minify-js", "duration": 5149, "timestamp": 109081062177, "id": 594, "parentId": 569, "tags": {"name": "../app/plans/page.js", "cache": "HIT"}, "startTime": 1750066095330, "traceId": "06a5c60055a7a519"}, {"name": "minify-js", "duration": 5139, "timestamp": 109081062190, "id": 595, "parentId": 569, "tags": {"name": "../app/refer/page.js", "cache": "HIT"}, "startTime": 1750066095330, "traceId": "06a5c60055a7a519"}, {"name": "minify-js", "duration": 5126, "timestamp": 109081062206, "id": 596, "parentId": 569, "tags": {"name": "../app/registration-diagnostics/page.js", "cache": "HIT"}, "startTime": 1750066095330, "traceId": "06a5c60055a7a519"}, {"name": "minify-js", "duration": 5114, "timestamp": 109081062221, "id": 597, "parentId": 569, "tags": {"name": "../app/support/page.js", "cache": "HIT"}, "startTime": 1750066095330, "traceId": "06a5c60055a7a519"}, {"name": "minify-js", "duration": 5104, "timestamp": 109081062234, "id": 598, "parentId": 569, "tags": {"name": "../app/profile/page.js", "cache": "HIT"}, "startTime": 1750066095330, "traceId": "06a5c60055a7a519"}, {"name": "minify-js", "duration": 5094, "timestamp": 109081062247, "id": 599, "parentId": 569, "tags": {"name": "../app/register/page.js", "cache": "HIT"}, "startTime": 1750066095330, "traceId": "06a5c60055a7a519"}]